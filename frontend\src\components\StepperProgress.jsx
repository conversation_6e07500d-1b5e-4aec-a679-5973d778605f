import React from 'react';
import '../styles/stepper.css';

/**
 * StepperProgress component for displaying a progress bar with steps
 * @param {Object} props - Component props
 * @param {number} props.currentStep - Current active step (1-based)
 * @param {Array} props.steps - Array of step labels
 * @param {boolean} props.showLabels - Whether to show step labels
 */
const StepperProgress = ({ currentStep, steps, showLabels = true }) => {
  // Calculate progress percentage
  const totalSteps = steps.length;
  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;

  // Width calculation for line between circles
  const adjustedWidth = `calc((66.67% - 60px) * ${progressPercentage / 100})`;

  return (
    <div className="stepper-progress">
      {/* Background line */}
      <div className="stepper-progress-line"></div>

      {/* Active progress line */}
      <div
        className="stepper-progress-line-active"
        style={{
          width: adjustedWidth
        }}
      ></div>

      {/* Steps */}
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;

        return (
          <div key={stepNumber} className="stepper-step">
            <div
              className={`stepper-step-circle ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''}`}
            >
              {isCompleted ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="checkmark-icon">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              ) : (
                stepNumber
              )}
            </div>
            {showLabels && (
              <div className={`stepper-step-label ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''}`}>
                {step}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default StepperProgress;
