import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import { showErrorMessage } from '../utils/toastNotifications';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/clients.css';

const CommissionTracker = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [commissions, setCommissions] = useState([]);
  const [filteredCommissions, setFilteredCommissions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [periodFilter, setPeriodFilter] = useState('all');
  const [stats, setStats] = useState({
    totalEarned: 0,
    thisMonth: 0,
    pending: 0,
    paid: 0,
    averageCommission: 0
  });

  useEffect(() => {
    fetchCommissions();
  }, []);

  useEffect(() => {
    filterCommissions();
  }, [commissions, searchTerm, statusFilter, periodFilter]);

  const fetchCommissions = async () => {
    try {
      setLoading(true);
      logger.info('Fetching commission data...');
      
      // Mock commission data
      const mockCommissions = [
        {
          id: '1',
          clientName: 'ABC Corporation',
          dealId: 'DEAL-001',
          contractValue: 15000,
          commissionRate: 5,
          commissionAmount: 750,
          status: 'Paid',
          earnedDate: '2024-01-15',
          paidDate: '2024-02-01',
          paymentMethod: 'Bank Transfer',
          supplier: 'EDF',
          energyType: 'Electricity'
        },
        {
          id: '2',
          clientName: 'Green Solutions Ltd',
          dealId: 'DEAL-002',
          contractValue: 8500,
          commissionRate: 5,
          commissionAmount: 425,
          status: 'Paid',
          earnedDate: '2023-12-01',
          paidDate: '2024-01-15',
          paymentMethod: 'Bank Transfer',
          supplier: 'Engie',
          energyType: 'Gas'
        },
        {
          id: '3',
          clientName: 'Tech Startup Inc',
          dealId: 'DEAL-003',
          contractValue: 12000,
          commissionRate: 5,
          commissionAmount: 600,
          status: 'Pending',
          earnedDate: '2024-02-01',
          paidDate: null,
          paymentMethod: null,
          supplier: 'Total Energies',
          energyType: 'Both'
        },
        {
          id: '4',
          clientName: 'Manufacturing Co',
          dealId: 'DEAL-004',
          contractValue: 25000,
          commissionRate: 4.5,
          commissionAmount: 1125,
          status: 'Processing',
          earnedDate: '2024-01-30',
          paidDate: null,
          paymentMethod: null,
          supplier: 'EDF',
          energyType: 'Electricity'
        },
        {
          id: '5',
          clientName: 'Retail Chain SA',
          dealId: 'DEAL-005',
          contractValue: 18000,
          commissionRate: 5,
          commissionAmount: 900,
          status: 'Paid',
          earnedDate: '2024-01-20',
          paidDate: '2024-02-05',
          paymentMethod: 'Bank Transfer',
          supplier: 'Engie',
          energyType: 'Electricity'
        }
      ];

      setCommissions(mockCommissions);
      
      // Calculate stats
      const totalEarned = mockCommissions.reduce((sum, comm) => sum + comm.commissionAmount, 0);
      const thisMonth = mockCommissions
        .filter(comm => new Date(comm.earnedDate).getMonth() === new Date().getMonth())
        .reduce((sum, comm) => sum + comm.commissionAmount, 0);
      const pending = mockCommissions
        .filter(comm => comm.status === 'Pending' || comm.status === 'Processing')
        .reduce((sum, comm) => sum + comm.commissionAmount, 0);
      const paid = mockCommissions
        .filter(comm => comm.status === 'Paid')
        .reduce((sum, comm) => sum + comm.commissionAmount, 0);
      
      setStats({
        totalEarned,
        thisMonth,
        pending,
        paid,
        averageCommission: totalEarned / mockCommissions.length
      });
      
      logger.info('Commission data fetched successfully');
    } catch (error) {
      logger.error('Error fetching commission data:', error);
      showErrorMessage('FETCH_COMMISSIONS_ERROR', 'Failed to load commission data');
    } finally {
      setLoading(false);
    }
  };

  const filterCommissions = () => {
    let filtered = commissions;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(comm =>
        comm.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        comm.dealId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        comm.supplier.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(comm => comm.status.toLowerCase() === statusFilter.toLowerCase());
    }

    // Filter by period
    if (periodFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (periodFilter) {
        case 'thisMonth':
          filtered = filtered.filter(comm => 
            new Date(comm.earnedDate).getMonth() === now.getMonth() &&
            new Date(comm.earnedDate).getFullYear() === now.getFullYear()
          );
          break;
        case 'lastMonth':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(comm => 
            new Date(comm.earnedDate).getMonth() === filterDate.getMonth() &&
            new Date(comm.earnedDate).getFullYear() === filterDate.getFullYear()
          );
          break;
        case 'last3Months':
          filterDate.setMonth(now.getMonth() - 3);
          filtered = filtered.filter(comm => new Date(comm.earnedDate) >= filterDate);
          break;
      }
    }

    setFilteredCommissions(filtered);
  };

  const getStatusBadgeClass = (status) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'status-badge completed';
      case 'pending':
        return 'status-badge pending';
      case 'processing':
        return 'status-badge active';
      default:
        return 'status-badge';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return dateString ? new Date(dateString).toLocaleDateString('fr-FR') : '-';
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="clients-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading commission data..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="clients-container">
        <div className="clients-header">
          <div className="header-content">
            <h1>
              <i className="fas fa-euro-sign"></i>
              Commission Tracker
            </h1>
            <p>Track your earnings and commission payments</p>
          </div>
          <div className="header-actions">
            <button className="btn-primary" onClick={() => navigate('/analytics')}>
              <i className="fas fa-chart-line"></i>
              View Analytics
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-coins"></i>
            </div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.totalEarned)}</h3>
              <p>Total Earned</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-calendar-month"></i>
            </div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.thisMonth)}</h3>
              <p>This Month</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.pending)}</h3>
              <p>Pending</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.paid)}</h3>
              <p>Paid</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="clients-filters">
          <div className="search-box">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="Search by client, deal ID, or supplier..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="paid">Paid</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
            </select>
            <select
              value={periodFilter}
              onChange={(e) => setPeriodFilter(e.target.value)}
            >
              <option value="all">All Time</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
              <option value="last3Months">Last 3 Months</option>
            </select>
          </div>
        </div>

        {/* Commission List */}
        <div className="clients-list">
          {filteredCommissions.length > 0 ? (
            filteredCommissions.map((commission) => (
              <div key={commission.id} className="client-card">
                <div className="client-header">
                  <div className="client-info">
                    <h3>{commission.clientName}</h3>
                    <p>Deal ID: {commission.dealId}</p>
                  </div>
                  <div className="client-status">
                    <span className={getStatusBadgeClass(commission.status)}>
                      {commission.status}
                    </span>
                  </div>
                </div>
                <div className="client-details">
                  <div className="detail-item">
                    <span className="label">Contract Value:</span>
                    <span className="value">{formatCurrency(commission.contractValue)}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Commission Rate:</span>
                    <span className="value">{commission.commissionRate}%</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Commission Amount:</span>
                    <span className="value commission">{formatCurrency(commission.commissionAmount)}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Supplier:</span>
                    <span className="value">{commission.supplier}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Energy Type:</span>
                    <span className="value">{commission.energyType}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Earned Date:</span>
                    <span className="value">{formatDate(commission.earnedDate)}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Paid Date:</span>
                    <span className="value">{formatDate(commission.paidDate)}</span>
                  </div>
                  {commission.paymentMethod && (
                    <div className="detail-item">
                      <span className="label">Payment Method:</span>
                      <span className="value">{commission.paymentMethod}</span>
                    </div>
                  )}
                </div>
                <div className="client-actions">
                  <button className="btn-secondary">
                    <i className="fas fa-eye"></i>
                    View Details
                  </button>
                  <button className="btn-secondary">
                    <i className="fas fa-download"></i>
                    Download Invoice
                  </button>
                  {commission.status === 'Pending' && (
                    <button className="btn-secondary">
                      <i className="fas fa-question-circle"></i>
                      Follow Up
                    </button>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="empty-state">
              <i className="fas fa-euro-sign"></i>
              <h3>No commissions found</h3>
              <p>
                {searchTerm || statusFilter !== 'all' || periodFilter !== 'all'
                  ? 'No commissions match your current filters.'
                  : 'You haven\'t earned any commissions yet.'}
              </p>
              <button className="btn-primary" onClick={() => navigate('/add-client')}>
                <i className="fas fa-plus"></i>
                Create Your First Deal
              </button>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CommissionTracker;
