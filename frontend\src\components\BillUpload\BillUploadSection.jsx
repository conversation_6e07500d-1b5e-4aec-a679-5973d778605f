import React, { useState } from 'react';
import FileUploader from './FileUploader';
import DataExtractionStep from './DataExtractionStep';
import AppointmentBookingStep from './AppointmentBookingStep';
import CompletionStep from './CompletionStep';
import StepperProgress from '../StepperProgress';
import billUploadService from '../../services/billUpload.service';
import '../../styles/bill-upload.css';

const BillUploadSection = () => {
  const [currentStep, setCurrentStep] = useState('upload'); // 'upload', 'extraction', 'appointment', 'complete'

  // Convert step names to numbers for StepperProgress
  const getStepNumber = (step) => {
    switch (step) {
      case 'upload': return 1;
      case 'extraction': return 2;
      case 'appointment': return 3;
      case 'complete': return 3; // Keep at 3 since completion is part of appointment step
      default: return 1;
    }
  };

  const stepLabels = ['Upload Bills', 'Verify Data', 'Book Appointment'];
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploadUUID, setUploadUUID] = useState(null);
  const [clientData, setClientData] = useState(null);
  const [clientId, setClientId] = useState(null);
  const [appointmentResult, setAppointmentResult] = useState(null);
  const [error, setError] = useState(null);

  const handleFileUpload = async (files) => {
    try {
      setError(null);
      setUploadedFiles(files);

      console.log('🚀 Starting file upload process...');

      // Upload files to S3
      const uploadResult = await billUploadService.uploadEnergyBills(files);

      if (uploadResult.success) {
        setUploadUUID(uploadResult.uploadUUID);
        console.log('✅ Files uploaded successfully, UUID:', uploadResult.uploadUUID);

        // Move to extraction step
        setCurrentStep('extraction');
      } else {
        throw new Error('Upload failed');
      }

    } catch (error) {
      console.error('❌ Upload error:', error);
      setError(error.message);
    }
  };

  const handleDataConfirmed = (confirmedData, newClientId) => {
    setClientData(confirmedData);
    setClientId(newClientId);
    setCurrentStep('appointment');
  };

  const handleAppointmentComplete = (result) => {
    setAppointmentResult(result);
    setCurrentStep('complete');
  };

  const handleBackToUpload = () => {
    setCurrentStep('upload');
    setUploadedFiles([]);
    setUploadUUID(null);
    setClientData(null);
    setClientId(null);
    setAppointmentResult(null);
    setError(null);
  };

  const handleBackToExtraction = () => {
    setCurrentStep('extraction');
  };

  const handleStartOver = () => {
    setCurrentStep('upload');
    setUploadedFiles([]);
    setUploadUUID(null);
    setClientData(null);
    setClientId(null);
    setAppointmentResult(null);
    setError(null);
  };

  return (
    <section className="bill-upload-section" id="bill-upload">
      <div className="bill-upload-container">
        <div className="bill-upload-header">
          <h2 className="bill-upload-title">
            Upload Your Energy Bill
          </h2>
          <p className="bill-upload-subtitle">
            Get personalized energy offers in minutes. Simply upload your electricity or gas bill and let our experts find you the best deals.
          </p>
        </div>

        {/* Progress Indicator */}
        <div style={{ margin: '40px auto 60px auto', maxWidth: '500px' }}>
          <StepperProgress
            currentStep={getStepNumber(currentStep)}
            steps={stepLabels}
            showLabels={true}
          />
        </div>

        <div className="bill-upload-content">
          {error && currentStep === 'upload' && (
            <div className="error-message">
              <div className="error-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                  <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
                </svg>
              </div>
              <div className="error-content">
                <h4>Upload Failed</h4>
                <p>{error}</p>
                <button className="btn btn-outline" onClick={() => setError(null)}>
                  Try Again
                </button>
              </div>
            </div>
          )}

          {currentStep === 'upload' && (
            <div className="upload-step">
              <div className="upload-benefits">
                <div className="benefit-item">
                  <div className="benefit-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                      <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                    </svg>
                  </div>
                  <span>100% Free Service</span>
                </div>
                <div className="benefit-item">
                  <div className="benefit-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                    </svg>
                  </div>
                  <span>Secure & Private</span>
                </div>
                <div className="benefit-item">
                  <div className="benefit-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                  </div>
                  <span>No Account Required</span>
                </div>
                <div className="benefit-item">
                  <div className="benefit-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>
                      <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"/>
                    </svg>
                  </div>
                  <span>Results in Minutes</span>
                </div>
              </div>

              <FileUploader onFileUpload={handleFileUpload} />

              <div className="upload-info">
                <h4>Accepted File Types:</h4>
                <div className="file-types">
                  <span className="file-type">PDF</span>
                  <span className="file-type">JPG</span>
                  <span className="file-type">PNG</span>
                  <span className="file-type">HEIC</span>
                </div>
                <p className="upload-note">
                  We accept electricity bills, gas bills, or combined energy contracts from any supplier.
                </p>
              </div>
            </div>
          )}

          {currentStep === 'extraction' && (
            <DataExtractionStep
              uploadUUID={uploadUUID}
              uploadedFiles={uploadedFiles}
              onDataConfirmed={handleDataConfirmed}
              onBack={handleBackToUpload}
            />
          )}

          {currentStep === 'appointment' && (
            <AppointmentBookingStep
              uploadUUID={uploadUUID}
              clientData={clientData}
              clientId={clientId}
              onBack={handleBackToExtraction}
              onComplete={handleAppointmentComplete}
            />
          )}

          {currentStep === 'complete' && (
            <CompletionStep
              appointmentResult={appointmentResult}
              clientData={clientData}
              onStartOver={handleStartOver}
            />
          )}
        </div>

        <div className="bill-upload-footer">
          <div className="security-badges">
            <div className="security-badge">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
              </svg>
              <span>GDPR Compliant</span>
            </div>
            <div className="security-badge">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M5.338 1.59a61.44 61.44 0 0 0-2.837.856.481.481 0 0 0-.328.39c-.554 4.157.726 7.19 2.253 9.188a10.725 10.725 0 0 0 2.287 2.233c.346.244.652.42.893.533.12.057.218.095.293.118a.55.55 0 0 0 .101.025.615.615 0 0 0 .1-.025c.076-.023.174-.061.294-.118.24-.113.547-.29.893-.533a10.726 10.726 0 0 0 2.287-2.233c1.527-1.997 2.807-5.031 2.253-9.188a.48.48 0 0 0-.328-.39c-.651-.213-1.75-.56-2.837-.855C9.552 1.29 8.531 1.067 8 1.067c-.53 0-1.552.223-2.662.524zM5.072.56C6.157.265 7.31 0 8 0s1.843.265 2.928.56c1.11.3 2.229.655 2.887.87a1.54 1.54 0 0 1 1.044 1.262c.596 4.477-.787 7.795-2.465 9.99a11.775 11.775 0 0 1-2.517 2.453 7.159 7.159 0 0 1-1.048.625c-.28.132-.581.24-.829.24s-.548-.108-.829-.24a7.158 7.158 0 0 1-1.048-.625 11.777 11.777 0 0 1-2.517-2.453C1.928 10.487.545 7.169 1.141 2.692A1.54 1.54 0 0 1 2.185 1.43 62.456 62.456 0 0 1 5.072.56z"/>
              </svg>
              <span>SSL Encrypted</span>
            </div>
            <div className="security-badge">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                <path fillRule="evenodd" d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"/>
                <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/>
              </svg>
              <span>Trusted by 10,000+ Users</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BillUploadSection;
