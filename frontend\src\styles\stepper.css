/* Stepper Component Styles */
.stepper-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
  min-height: calc(100vh - 140px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepper-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 3rem 2.5rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  max-width: 900px;
}

.stepper-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #000000, #000000);
}

/* Progress Bar Styles */
.stepper-progress {
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
  margin-bottom: 3rem;
  position: relative;
  width: 100%;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  padding: 0;
}

.stepper-progress-line {
  position: absolute;
  top: 20px;
  left: calc(16.67% + 25px);
  width: calc(66.67% - 50px);
  height: 3px;
  background-color: #e0e0e0;
  z-index: 1;
  border-radius: 2px;
}

.stepper-progress-line-active {
  position: absolute;
  top: 20px;
  left: calc(16.67% + 25px);
  height: 3px;
  background: linear-gradient(90deg, #000000, #000000);
  z-index: 2;
  border-radius: 2px;
  transition: width 0.5s ease;
  transform-origin: left;
}

.stepper-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
  flex: 1;
  text-align: center;
}

.stepper-step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  border: 3px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: #888;
  margin-bottom: 1rem;
  margin-top: 0;
  transition: all 0.3s ease;
}

.stepper-step-circle.active {
  border-color: #000;
  color: #000;
  background-color: #fff;
}

.stepper-step-circle.completed {
  border-color: #000;
  background-color: #000;
  color: #fff;
}

.checkmark-icon {
  max-width: 100%;
  max-height: 100%;
}

.stepper-step-label {
  font-size: 0.9rem;
  color: #000; /* Changed from #666 to black */
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
  background-color: transparent; /* Ensure no background color */
  padding: 2px 5px;
}

.stepper-step-label.active {
  color: #000;
  font-weight: 600;
}

.stepper-step-label.completed {
  color: #000; /* Changed from #2ecc71 to black */
  font-weight: 600;
}

/* Form Styles */
.stepper-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.hint-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Professional info page specific overrides - match individual info page layout */
.professional-info-page .stepper-container {
  max-width: 1000px !important;
  width: 100% !important;
  padding: 1rem !important;
  margin: 0 auto !important;
  min-height: calc(100vh - 140px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.professional-info-page .stepper-content {
  max-width: 900px !important;
  width: 100% !important;
  padding: 3rem 2.5rem !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border-radius: 12px !important;
  background-color: #fff !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.professional-info-page .stepper-content::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 6px !important;
  background: linear-gradient(90deg, #000000, #000000) !important;
}

.professional-info-page .stepper-form {
  max-width: 750px !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Supplier info page specific overrides - wider form to reduce empty spaces */
.supplier-info-page .stepper-container {
  max-width: 1000px !important;
  width: 100% !important;
  padding: 1rem !important;
  margin: 0 auto !important;
  min-height: calc(100vh - 140px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.supplier-info-page .stepper-content {
  max-width: 900px !important;
  width: 100% !important;
  padding: 3rem 2.5rem !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border-radius: 12px !important;
  background-color: #fff !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.supplier-info-page .stepper-content::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 6px !important;
  background: linear-gradient(90deg, #000000, #000000) !important;
}

.supplier-info-page .stepper-form {
  max-width: 1200px !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

.professional-info-page .stepper-progress {
  max-width: 100% !important;
  width: 100% !important;
}

/* Make form groups behave properly in professional info */
.professional-info-page .form-group {
  width: 100% !important;
  max-width: 100% !important;
  margin-bottom: 1rem !important;
}

.professional-info-page .form-input {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0.8rem 1rem !important;
  height: auto !important;
  font-size: 1rem !important;
  margin-bottom: 0.5rem !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
}

/* Fix icon and input overlap for professional info page */
.professional-info-page .input-with-icon .form-input {
  padding-left: 45px !important;
}

.professional-info-page .input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  z-index: 2 !important;
  pointer-events: none !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Supplier info page form styling - clean design without icons, full width fields */
.supplier-info-page .stepper-progress {
  max-width: 500px !important;
  width: 100% !important;
}

/* Override main container width for supplier info page */
.supplier-info-page .step-content {
  max-width: 1200px !important;
  width: 100% !important;
  margin: 0 auto !important;
}

/* Make form container much wider for supplier info page to match reference design */
.supplier-info-page .stepper-form {
  max-width: 1200px !important;
  width: 100% !important;
  padding: 0 1rem !important;
}

/* Make form rows span much wider for supplier info page */
.supplier-info-page .form-row {
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.supplier-info-page .form-group {
  width: 100% !important;
  max-width: 100% !important;
  margin-bottom: 1.5rem !important;
}

/* Clean form inputs without icons for supplier info page */
.supplier-info-page .form-input {
  width: 100% !important;
  max-width: 100% !important;
  padding: 1rem !important;
  height: 48px !important;
  font-size: 1rem !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  background-color: #fff !important;
  box-sizing: border-box !important;
}

/* Textarea specific styling for supplier info page */
.supplier-info-page textarea.form-input {
  height: 80px !important;
  min-height: 80px !important;
  padding: 1rem !important;
  resize: vertical !important;
}

/* Focus states for supplier info page */
.supplier-info-page .form-input:focus {
  border-color: #000 !important;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}

/* Required field indicator */
.required {
  color: #e74c3c;
  margin-left: 3px;
}

.optional {
  color: #777;
  font-size: 0.85rem;
  font-weight: normal;
  margin-left: 3px;
}

/* Checkbox styling */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-top: 0.2rem;
  margin-right: 0.5rem;
  accent-color: #000;
  cursor: pointer;
}

.checkbox-item label {
  font-size: 0.95rem;
  line-height: 1.4;
  cursor: pointer;
}

/* Authorization checkbox specific styling */
.authorization-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin: 1rem 0;
  padding: 1rem;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.authorization-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin-top: 0;
  margin-right: 0;
  accent-color: #000;
  cursor: pointer;
  flex-shrink: 0;
}

.authorization-checkbox label {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #333;
  cursor: pointer;
  margin: 0;
}

/* Error message for authorization checkbox */
.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Hint text */
.hint-text {
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.5rem;
}

.form-group {
  width: 100%;
  margin-bottom: 1.5rem;
  text-align: left;
  position: relative;
}

/* Header Section */
.stepper-header {
  width: 100%;
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem 0;
}

/* Page title styling */
.page-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #000;
  position: relative;
  display: inline-block;
  text-align: center;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #000000, #000000);
  border-radius: 2px;
}

.form-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* Two-column layout for company info step */
.form-row.two-column {
  flex-direction: row;
  gap: 1.5rem;
  align-items: flex-start;
  max-width: 800px;
}

.form-col {
  flex: 1;
  width: 100%;
}

/* Responsive behavior for two-column layout */
@media (max-width: 768px) {
  .form-row.two-column {
    flex-direction: column;
    gap: 0;
  }
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333 !important;
}

.form-input {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  max-width: 100%;
  background-color: #fff;
  line-height: 1.5 !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
  height: 48px !important;
}

/* Hide number input spinners in stepper */
.form-input[type="number"]::-webkit-outer-spin-button,
.form-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-input[type="number"] {
  -moz-appearance: textfield;
}

/* Textarea specific styling in stepper */
textarea.form-input {
  min-height: 80px !important;
  max-height: 200px !important;
  height: 80px !important;
  resize: vertical !important;
  line-height: 1.4 !important;
  padding: 12px 1rem !important;
  align-items: flex-start !important;
  display: block !important;
  overflow-y: auto !important;
}

.form-input:focus {
  border-color: #000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  outline: none;
}

.form-input.error {
  border-color: #e74c3c;
}

.form-error {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 0.4rem;
  text-align: left;
  width: 100%;
}

/* Input with icon styling */
.input-with-icon {
  position: relative !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  height: 48px !important;
}

/* Universal Input Icon - Perfect Vertical Centering in Stepper */
.input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: none !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;
}

.input-icon svg {
  width: 18px !important;
  height: 18px !important;
  vertical-align: middle !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
}

.input-with-icon .form-input {
  padding-left: 45px !important;
  text-indent: 0 !important;
  vertical-align: middle !important;
  line-height: 1.5 !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  height: 48px !important;
  margin: 0 !important;
}

/* Textarea with icon in stepper */
.input-with-icon textarea.form-input {
  padding-left: 45px !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  display: block !important;
  align-items: flex-start !important;
  min-height: 80px !important;
  height: 80px !important;
  max-height: 200px !important;
}

/* Textarea container override in stepper */
.input-with-icon:has(textarea) {
  height: auto !important;
  align-items: flex-start !important;
  min-height: 80px !important;
}

/* Icon positioning for textarea in stepper */
.input-with-icon:has(textarea) .input-icon {
  top: 24px !important;
  transform: none !important;
}

/* Direct targeting for textarea icons in stepper */
.input-with-icon span.input-icon {
  position: absolute !important;
  left: 16px !important;
  z-index: 2 !important;
}

.input-with-icon:has(textarea) span.input-icon {
  top: 24px !important;
  transform: none !important;
}

/* Specific targeting for Business Description in stepper */
#businessDescription ~ .input-icon,
.input-with-icon:has(#businessDescription) .input-icon {
  top: 24px !important;
  transform: none !important;
  left: 16px !important;
  position: absolute !important;
}

/* Universal Stepper Form Field Alignment */
/* Ensure consistent alignment across all stepper forms */

/* All stepper input containers with icons */
.input-with-icon {
  position: relative !important;
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  height: 48px !important;
}

/* All stepper form inputs with perfect vertical centering */
.input-with-icon .form-input {
  padding-left: 45px !important;
  text-indent: 0 !important;
  vertical-align: middle !important;
  line-height: 1.5 !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  height: 48px !important;
  margin: 0 !important;
}

/* Perfect placeholder alignment in stepper */
.input-with-icon .form-input::placeholder {
  color: #aaa !important;
  opacity: 0.8 !important;
  line-height: 1.5 !important;
  vertical-align: middle !important;
  text-align: left !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure all stepper icons are perfectly centered */
.input-with-icon .input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #666 !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  pointer-events: none !important;
}

/* Ensure perfect horizontal alignment for stepper inputs */
.input-with-icon input.form-input {
  text-align: left;
  padding-top: 0;
  padding-bottom: 0;
  line-height: 1.5;
  vertical-align: middle;
}

/* Specific alignment for number inputs in stepper */
.input-with-icon input[type="number"].form-input {
  text-align: left;
}

/* Label styling */
label {
  font-weight: 500;
  color: #333 !important;
  margin-bottom: 0.5rem;
  display: block;
}

/* Confirmation Summary Styles */
.confirmation-summary {
  width: 100%;
  margin-bottom: 2rem;
  text-align: left;
}

.confirmation-summary h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

/* Make stepper summary sections more specific to override other styles */
.confirmation-summary .summary-section,
.stepper-form .summary-section {
  margin-bottom: 1.5rem;
  background-color: #fafafa;
  border-radius: 6px;
  padding: 1.25rem;
  border: 1px solid #e8e8e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.confirmation-summary .summary-section h5,
.stepper-form .summary-section h5 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 0.85rem !important;
  color: #2c3e50;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.5rem;
  font-weight: 500 !important;
  letter-spacing: 0.2px;
}

/* Make stepper summary rows more specific to override other styles */
.confirmation-summary .summary-row,
.stepper-form .summary-row {
  display: flex;
  margin-bottom: 0.7rem;
  font-size: 0.75rem;
  line-height: 1.4;
  align-items: flex-start;
  padding: 0.25rem 0;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.confirmation-summary .summary-row:hover,
.stepper-form .summary-row:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Make stepper summary labels more specific to override other styles */
.confirmation-summary .summary-label,
.stepper-form .summary-label {
  font-weight: 500;
  width: 35%;
  color: #5a6c7d;
  font-size: 0.9rem !important;
  text-transform: capitalize;
}

/* Make stepper summary values more specific to override other styles */
.confirmation-summary .summary-value,
.stepper-form .summary-value {
  width: 65%;
  font-weight: 500 !important;
  color: #2c3e50;
  font-size: 14px !important;
  line-height: 1.4;
  word-break: break-word;
  padding-left: 0.5rem;
}

/* Button Styles */
.stepper-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  width: 100%;
}

.stepper-button {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

/* Next Button - Primary Action */
.stepper-button-next,
button.stepper-button-next,
.stepper-buttons .stepper-button-next {
  background-color: #000 !important;
  background-image: none !important;
  color: #fff !important;
  border: 2px solid #000 !important;
  border-radius: 8px !important;
  padding: 0.8rem 1.5rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  min-width: 120px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.stepper-button-next:hover,
button.stepper-button-next:hover,
.stepper-buttons .stepper-button-next:hover {
  background-color: #333 !important;
  background-image: none !important;
  color: #fff !important;
  border-color: #333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25) !important;
}

.stepper-button-next:focus,
button.stepper-button-next:focus,
.stepper-buttons .stepper-button-next:focus {
  background-color: #000 !important;
  background-image: none !important;
  color: #fff !important;
  border-color: #000 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
}

.stepper-button-prev {
  background-color: #fff;
  color: #333;
  border: 1px solid #ddd;
}

.stepper-button-prev:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

/* Disabled State */
.stepper-button:disabled,
.stepper-button-next:disabled,
button.stepper-button-next:disabled,
.stepper-buttons .stepper-button-next:disabled {
  background-color: #666 !important;
  background-image: none !important;
  color: #fff !important;
  border-color: #666 !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* ========== MAXIMUM SPECIFICITY STEPPER BUTTON OVERRIDES ========== */
/* These overrides use maximum CSS specificity to ensure black buttons */

html body div.fullscreen-container.professional-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next,
html body div.fullscreen-container.individual-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next,
html body div.fullscreen-container.broker-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next,
html body div.fullscreen-container.supplier-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next,
html body .professional-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next,
html body .individual-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next,
html body .broker-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next,
html body .supplier-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next,
html body .stepper-container .stepper-content .stepper-form .stepper-buttons button.stepper-button.stepper-button-next,
html body .stepper-container .stepper-content .stepper-form button[type="submit"].stepper-button.stepper-button-next,
html body .stepper-container .stepper-content .stepper-form button[type="button"].stepper-button.stepper-button-next {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
  border-color: #000000 !important;
  border-top-color: #000000 !important;
  border-right-color: #000000 !important;
  border-bottom-color: #000000 !important;
  border-left-color: #000000 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

html body div.fullscreen-container.professional-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next:hover,
html body div.fullscreen-container.individual-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next:hover,
html body div.fullscreen-container.broker-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next:hover,
html body div.fullscreen-container.supplier-info-page div.content-wrapper div.wizard-fullscreen div.stepper-container div.stepper-content div.stepper-form button.stepper-button.stepper-button-next:hover,
html body .professional-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next:hover,
html body .individual-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next:hover,
html body .broker-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next:hover,
html body .supplier-info-page .stepper-container .stepper-content .stepper-form button.stepper-button.stepper-button-next:hover,
html body .stepper-container .stepper-content .stepper-form .stepper-buttons button.stepper-button.stepper-button-next:hover,
html body .stepper-container .stepper-content .stepper-form button[type="submit"].stepper-button.stepper-button-next:hover,
html body .stepper-container .stepper-content .stepper-form button[type="button"].stepper-button.stepper-button-next:hover {
  background-color: #333333 !important;
  background-image: none !important;
  background: #333333 !important;
  color: #ffffff !important;
  border: 2px solid #333333 !important;
  border-color: #333333 !important;
  border-top-color: #333333 !important;
  border-right-color: #333333 !important;
  border-bottom-color: #333333 !important;
  border-left-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25) !important;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .stepper-container {
    padding: 1rem 0.5rem;
    min-height: calc(100vh - 120px);
  }

  .stepper-content {
    padding: 2rem 1.5rem;
    max-width: 100%;
  }

  .stepper-step-label {
    font-size: 0.8rem;
    max-width: 90px;
    text-align: center;
    line-height: 1.3;
    color: #000;
    background-color: transparent;
  }

  .stepper-buttons {
    flex-direction: column-reverse;
    gap: 1rem;
    width: 100%;
  }

  .stepper-button {
    width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.2rem;
  }

  .form-input {
    padding: 0.7rem 0.9rem;
  }

  .stepper-progress {
    margin-bottom: 2rem;
    max-width: 90%;
    padding: 0;
  }

  .stepper-progress-line {
    top: 18px;
    left: calc(16.67% + 22px);
    width: calc(66.67% - 44px);
  }

  .stepper-progress-line-active {
    top: 18px;
    left: calc(16.67% + 22px);
  }

  .stepper-step-circle {
    width: 36px;
    height: 36px;
    margin-bottom: 0.8rem;
  }

  .stepper-step {
    padding: 0 5px;
  }

  /* Mobile improvements for confirmation display */
  .confirmation-summary .summary-section,
  .stepper-form .summary-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .confirmation-summary .summary-label,
  .stepper-form .summary-label {
    font-size: 0.85rem !important;
    width: 40%;
  }

  .confirmation-summary .summary-value,
  .stepper-form .summary-value {
    font-size: 13px !important;
    width: 60%;
    padding-left: 0.25rem;
  }

  .confirmation-summary h4 {
    font-size: 0.95rem;
    margin-bottom: 1rem;
  }

  .confirmation-summary .summary-section h5,
  .stepper-form .summary-section h5 {
    font-size: 0.9rem !important;
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 480px) {
  .stepper-container {
    padding: 0.5rem 0.25rem;
  }

  .stepper-content {
    padding: 1.25rem 0.75rem;
  }

  .stepper-progress {
    margin-bottom: 1.5rem;
    max-width: 100%;
    padding: 0;
  }

  .stepper-step {
    width: auto;
    padding: 0 5px;
  }

  .stepper-step-circle {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    border-width: 2px;
  }

  .stepper-step-label {
    font-size: 0.65rem;
    max-width: 80px;
    white-space: normal;
    text-align: center;
    line-height: 1.2;
    color: #000;
    background-color: transparent;
  }

  .stepper-progress-line {
    top: 14px;
    left: calc(16.67% + 18px);
    width: calc(66.67% - 36px);
    height: 2px;
  }

  .stepper-progress-line-active {
    top: 14px;
    left: calc(16.67% + 18px);
    height: 2px;
  }

  .stepper-form {
    padding: 0;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  /* Even smaller confirmation display for small mobile */
  .confirmation-summary .summary-label,
  .stepper-form .summary-label {
    font-size: 0.8rem !important;
  }

  .confirmation-summary .summary-value,
  .stepper-form .summary-value {
    font-size: 12px !important;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .stepper-step-circle {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    border-width: 2px;
  }

  .stepper-step-label {
    font-size: 0.6rem;
    max-width: 70px;
    color: #000;
    background-color: transparent;
  }

  .stepper-progress-line,
  .stepper-progress-line-active {
    top: 12px;
  }

  .stepper-content {
    padding: 1rem 0.5rem;
  }
}

/* ========== WIZARD LAYOUT FIXES ========== */
/* Proper wizard layout without !important overuse */

.wizard-step {
  width: 100%;
  display: block;
}

.wizard-step form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.wizard-content {
  width: 100%;
  display: block;
}

.form-group-half {
  width: 48%;
  flex: 0 0 48%;
  display: inline-block;
  float: left;
  margin-right: 1%;
  margin-left: 1%;
}

.wizard-buttons {
  width: 100%;
  clear: both;
  margin-top: 20px;
}

.wizard-title {
  width: 100%;
  display: block;
  clear: both;
}

@media (max-width: 576px) {
  .form-group-half {
    width: 100%;
    flex: 0 0 100%;
    margin-right: 0;
    margin-left: 0;
  }
}

/* ULTIMATE OVERRIDE - Maximum specificity for blue button fix */
html body div div div div div div button.stepper-button-next,
html body div div div div div button.stepper-button-next,
html body div div div div button.stepper-button-next,
html body div div div button.stepper-button-next,
html body div div button.stepper-button-next,
html body div button.stepper-button-next,
html body button.stepper-button-next,
body button.stepper-button-next,
button.stepper-button-next,
/* Add even more specific selectors */
html body div div div div div div button.stepper-button.stepper-button-next,
html body div div div div div button.stepper-button.stepper-button-next,
html body div div div div button.stepper-button.stepper-button-next,
html body div div div button.stepper-button.stepper-button-next,
html body div div button.stepper-button.stepper-button-next,
html body div button.stepper-button.stepper-button-next,
html body button.stepper-button.stepper-button-next,
body button.stepper-button.stepper-button-next,
button.stepper-button.stepper-button-next,
/* Target all possible button types */
html body div div div div div div button[type="button"].stepper-button-next,
html body div div div div div button[type="button"].stepper-button-next,
html body div div div div button[type="button"].stepper-button-next,
html body div div div button[type="button"].stepper-button-next,
html body div div button[type="button"].stepper-button-next,
html body div button[type="button"].stepper-button-next,
html body button[type="button"].stepper-button-next,
body button[type="button"].stepper-button-next,
button[type="button"].stepper-button-next,
/* Target submit buttons too */
html body div div div div div div button[type="submit"].stepper-button-next,
html body div div div div div button[type="submit"].stepper-button-next,
html body div div div div button[type="submit"].stepper-button-next,
html body div div div button[type="submit"].stepper-button-next,
html body div div button[type="submit"].stepper-button-next,
html body div button[type="submit"].stepper-button-next,
html body button[type="submit"].stepper-button-next,
body button[type="submit"].stepper-button-next,
button[type="submit"].stepper-button-next {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
  border-color: #000000 !important;
  border-top-color: #000000 !important;
  border-right-color: #000000 !important;
  border-bottom-color: #000000 !important;
  border-left-color: #000000 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  /* Add CSS custom properties to override any external styles */
  --bs-btn-bg: #000000 !important;
  --bs-btn-border-color: #000000 !important;
  --bs-btn-color: #ffffff !important;
  --btn-bg: #000000 !important;
  --btn-color: #ffffff !important;
  --button-bg: #000000 !important;
  --button-color: #ffffff !important;
}

html body div div div div div div button.stepper-button-next:hover,
html body div div div div div button.stepper-button-next:hover,
html body div div div div button.stepper-button-next:hover,
html body div div div button.stepper-button-next:hover,
html body div div button.stepper-button-next:hover,
html body div button.stepper-button-next:hover,
html body button.stepper-button-next:hover,
body button.stepper-button-next:hover,
button.stepper-button-next:hover,
/* Add hover states for all the new selectors */
html body div div div div div div button.stepper-button.stepper-button-next:hover,
html body div div div div div button.stepper-button.stepper-button-next:hover,
html body div div div div button.stepper-button.stepper-button-next:hover,
html body div div div button.stepper-button.stepper-button-next:hover,
html body div div button.stepper-button.stepper-button-next:hover,
html body div button.stepper-button.stepper-button-next:hover,
html body button.stepper-button.stepper-button-next:hover,
body button.stepper-button.stepper-button-next:hover,
button.stepper-button.stepper-button-next:hover,
html body div div div div div div button[type="button"].stepper-button-next:hover,
html body div div div div div button[type="button"].stepper-button-next:hover,
html body div div div div button[type="button"].stepper-button-next:hover,
html body div div div button[type="button"].stepper-button-next:hover,
html body div div button[type="button"].stepper-button-next:hover,
html body div button[type="button"].stepper-button-next:hover,
html body button[type="button"].stepper-button-next:hover,
body button[type="button"].stepper-button-next:hover,
button[type="button"].stepper-button-next:hover,
html body div div div div div div button[type="submit"].stepper-button-next:hover,
html body div div div div div button[type="submit"].stepper-button-next:hover,
html body div div div div button[type="submit"].stepper-button-next:hover,
html body div div div button[type="submit"].stepper-button-next:hover,
html body div div button[type="submit"].stepper-button-next:hover,
html body div button[type="submit"].stepper-button-next:hover,
html body button[type="submit"].stepper-button-next:hover,
body button[type="submit"].stepper-button-next:hover,
button[type="submit"].stepper-button-next:hover {
  background-color: #333333 !important;
  background-image: none !important;
  background: #333333 !important;
  color: #ffffff !important;
  border: 2px solid #333333 !important;
  border-color: #333333 !important;
  border-top-color: #333333 !important;
  border-right-color: #333333 !important;
  border-bottom-color: #333333 !important;
  border-left-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25) !important;
  /* Add CSS custom properties for hover */
  --bs-btn-bg: #333333 !important;
  --bs-btn-border-color: #333333 !important;
  --bs-btn-color: #ffffff !important;
  --btn-bg: #333333 !important;
  --btn-color: #ffffff !important;
  --button-bg: #333333 !important;
  --button-color: #ffffff !important;
}

html body div div div div div div button.stepper-button-next:focus,
html body div div div div div button.stepper-button-next:focus,
html body div div div div button.stepper-button-next:focus,
html body div div div button.stepper-button-next:focus,
html body div div button.stepper-button-next:focus,
html body div button.stepper-button-next:focus,
html body button.stepper-button-next:focus,
body button.stepper-button-next:focus,
button.stepper-button-next:focus {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
  border-color: #000000 !important;
  border-top-color: #000000 !important;
  border-right-color: #000000 !important;
  border-bottom-color: #000000 !important;
  border-left-color: #000000 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
}
