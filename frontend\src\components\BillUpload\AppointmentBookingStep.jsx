import React, { useState, useEffect } from 'react';
import billUploadService from '../../services/billUpload.service';

const AppointmentBookingStep = ({ uploadUUID, clientData, clientId, onBack, onComplete }) => {
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [contactMethod, setContactMethod] = useState('phone');
  const [customMessage, setCustomMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAvailableSlots();
  }, []);

  const fetchAvailableSlots = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await billUploadService.getAvailableSlots();
      
      if (result.success) {
        setAvailableSlots(result.slots);
      } else {
        throw new Error('Failed to fetch available slots');
      }
      
    } catch (error) {
      console.error('❌ Error fetching slots:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSlotSelection = (slot) => {
    setSelectedSlot(slot);
  };

  const handleBookAppointment = async () => {
    if (!selectedSlot) {
      alert('Please select an appointment slot');
      return;
    }

    try {
      setIsBooking(true);
      setError(null);
      
      const appointmentData = {
        date: selectedSlot.date,
        time: selectedSlot.time,
        contactMethod,
        customMessage,
        clientData,
        clientId
      };
      
      console.log('📅 Booking appointment:', appointmentData);
      
      const result = await billUploadService.bookAppointment(uploadUUID, appointmentData);
      
      if (result.success) {
        console.log('✅ Appointment booked successfully');
        onComplete(result);
      } else {
        throw new Error('Failed to book appointment');
      }
      
    } catch (error) {
      console.error('❌ Error booking appointment:', error);
      setError(error.message);
    } finally {
      setIsBooking(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  if (isLoading) {
    return (
      <div className="appointment-step">
        <div className="appointment-header">
          <h3>Loading Available Appointments</h3>
          <p>Fetching available time slots from our calendar...</p>
        </div>
        <div className="loading-spinner">
          <div className="processing-spinner"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="appointment-step">
        <div className="appointment-error">
          <div className="error-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
            </svg>
          </div>
          <h3>Unable to Load Appointments</h3>
          <p>{error}</p>
          <div className="error-actions">
            <button className="btn btn-black" onClick={fetchAvailableSlots}>
              Try Again
            </button>
            <button className="btn btn-outline" onClick={onBack}>
              Back to Data Review
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-step">
      <div className="appointment-header">
        <h3>Schedule Your Consultation</h3>
        <p>Choose a convenient time to discuss your energy optimization with our expert</p>
      </div>

      <div className="appointment-content">
        <div className="client-summary">
          <h4>Consultation for:</h4>
          <div className="client-info">
            <p><strong>{clientData.firstName} {clientData.lastName}</strong></p>
            <p>{clientData.email}</p>
            {clientData.companyName && <p><em>{clientData.companyName}</em></p>}
          </div>
        </div>

        <div className="appointment-form">
          <div className="form-section">
            <h4>Available Time Slots</h4>
            <div className="slots-grid">
              {availableSlots.map((slot, index) => (
                <div
                  key={index}
                  className={`slot-card ${selectedSlot === slot ? 'selected' : ''}`}
                  onClick={() => handleSlotSelection(slot)}
                >
                  <div className="slot-date">{formatDate(slot.date)}</div>
                  <div className="slot-time">{formatTime(slot.time)}</div>
                  <div className="slot-status">
                    {slot.available ? 'Available' : 'Unavailable'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="form-section">
            <h4>Contact Preferences</h4>
            <div className="contact-methods">
              <label className="contact-method">
                <input
                  type="radio"
                  name="contactMethod"
                  value="phone"
                  checked={contactMethod === 'phone'}
                  onChange={(e) => setContactMethod(e.target.value)}
                />
                <div className="method-info">
                  <div className="method-icon">📞</div>
                  <div className="method-details">
                    <strong>Phone Call</strong>
                    <p>Direct phone consultation</p>
                  </div>
                </div>
              </label>

              <label className="contact-method">
                <input
                  type="radio"
                  name="contactMethod"
                  value="video"
                  checked={contactMethod === 'video'}
                  onChange={(e) => setContactMethod(e.target.value)}
                />
                <div className="method-info">
                  <div className="method-icon">📹</div>
                  <div className="method-details">
                    <strong>Video Call</strong>
                    <p>Video consultation via Google Meet</p>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <div className="form-section">
            <h4>Additional Information (Optional)</h4>
            <div className="form-group">
              <label htmlFor="customMessage">Special requests or questions:</label>
              <textarea
                id="customMessage"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                rows="4"
                placeholder="Any specific questions about your energy bills or preferences for the consultation..."
              />
            </div>
          </div>

          <div className="appointment-summary">
            {selectedSlot && (
              <div className="selected-appointment">
                <h4>Selected Appointment:</h4>
                <div className="appointment-details">
                  <div className="detail-item">
                    <span className="detail-label">Date & Time:</span>
                    <span className="detail-value">
                      {formatDate(selectedSlot.date)} at {formatTime(selectedSlot.time)}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Contact Method:</span>
                    <span className="detail-value">
                      {contactMethod === 'phone' ? 'Phone Call' : 'Video Call'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="form-actions">
            <button className="btn btn-outline" onClick={onBack}>
              Back to Data Review
            </button>
            <button 
              className="btn btn-black btn-large"
              onClick={handleBookAppointment}
              disabled={!selectedSlot || isBooking}
            >
              {isBooking ? (
                <>
                  <div className="btn-spinner"></div>
                  Booking Appointment...
                </>
              ) : (
                'Confirm Appointment'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentBookingStep;
