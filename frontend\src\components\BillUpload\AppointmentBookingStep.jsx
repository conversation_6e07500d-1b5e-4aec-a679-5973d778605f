import React, { useState, useEffect } from 'react';
import billUploadService from '../../services/billUpload.service';

const AppointmentBookingStep = ({ uploadUUID, clientData, clientId, onBack, onComplete }) => {
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [contactMethod, setContactMethod] = useState('phone');
  const [customMessage, setCustomMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAvailableSlots();
  }, []);

  const fetchAvailableSlots = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await billUploadService.getAvailableSlots();
      
      if (result.success) {
        setAvailableSlots(result.slots);
      } else {
        throw new Error('Failed to fetch available slots');
      }
      
    } catch (error) {
      console.error('❌ Error fetching slots:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSlotSelection = (slot) => {
    setSelectedSlot(slot);
  };

  const handleBookAppointment = async () => {
    if (!selectedSlot) {
      alert('Please select an appointment slot');
      return;
    }

    try {
      setIsBooking(true);
      setError(null);
      
      const appointmentData = {
        date: selectedSlot.date,
        time: selectedSlot.time,
        contactMethod,
        customMessage,
        clientData,
        clientId
      };
      
      console.log('📅 Booking appointment:', appointmentData);
      
      const result = await billUploadService.bookAppointment(uploadUUID, appointmentData);
      
      if (result.success) {
        console.log('✅ Appointment booked successfully');
        onComplete(result);
      } else {
        throw new Error('Failed to book appointment');
      }
      
    } catch (error) {
      console.error('❌ Error booking appointment:', error);
      setError(error.message);
    } finally {
      setIsBooking(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  if (isLoading) {
    return (
      <div className="appointment-step">
        <div className="appointment-header">
          <h3>Loading Available Appointments</h3>
          <p>Fetching available time slots from our calendar...</p>
        </div>
        <div className="loading-spinner">
          <div className="processing-spinner"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="appointment-step">
        <div className="appointment-error">
          <div className="error-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
            </svg>
          </div>
          <h3>Unable to Load Appointments</h3>
          <p>{error}</p>
          <div className="error-actions">
            <button className="btn btn-black" onClick={fetchAvailableSlots}>
              Try Again
            </button>
            <button className="btn btn-outline" onClick={onBack}>
              Back to Data Review
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="appointment-step">
      <div className="appointment-header">
        <div className="header-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 2V5M16 2V5M3.5 9.09H20.5M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M15.6947 13.7002H15.7037M11.9955 13.7002H12.0045M8.29431 13.7002H8.30329M15.6947 17.0002H15.7037M11.9955 17.0002H12.0045M8.29431 17.0002H8.30329" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <h3>Schedule Your Energy Consultation</h3>
        <p>Book a 45-minute session with our energy expert to review your bill and explore savings opportunities</p>
      </div>

      <div className="appointment-content">
        {/* Client Summary Card */}
        <div className="client-summary-card">
          <div className="card-header">
            <div className="client-avatar">
              {clientData.companyName ? (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17 20H22V18H20V11H18V13H16V11H14V13H12V11H10V13H8V11H6V13H4V11H2V18H7V20H17ZM19 9V7H17V9H19ZM15 9V7H13V9H15ZM11 9V7H9V9H11ZM7 9V7H5V9H7Z" fill="currentColor"/>
                </svg>
              ) : (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="currentColor"/>
                  <path d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="currentColor"/>
                </svg>
              )}
            </div>
            <div className="client-details">
              <h4>{clientData.companyName || clientData.fullName}</h4>
              <p>{clientData.email}</p>
              <span className="client-type">{clientData.companyName ? 'Professional' : 'Individual'}</span>
            </div>
          </div>
        </div>

        <div className="appointment-form">
          {/* Time Slots Section */}
          <div className="form-section">
            <div className="section-header">
              <h4>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2"/>
                </svg>
                Choose Your Preferred Time
              </h4>
              <p>All times are shown in your local timezone (CET)</p>
            </div>

            <div className="slots-container">
              {availableSlots.length === 0 ? (
                <div className="no-slots-message">
                  <div className="no-slots-icon">📅</div>
                  <h5>No available slots</h5>
                  <p>Please contact us directly to schedule your consultation</p>
                </div>
              ) : (
                <div className="slots-grid">
                  {availableSlots.map((slot, index) => (
                    <div
                      key={index}
                      className={`slot-card ${selectedSlot === slot ? 'selected' : ''} ${!slot.available ? 'disabled' : ''}`}
                      onClick={() => slot.available && handleSlotSelection(slot)}
                    >
                      <div className="slot-header">
                        <div className="slot-date">{formatDate(slot.date)}</div>
                        <div className={`slot-status ${slot.available ? 'available' : 'unavailable'}`}>
                          {slot.available ? (
                            <><span className="status-dot"></span>Available</>
                          ) : (
                            <><span className="status-dot"></span>Booked</>
                          )}
                        </div>
                      </div>
                      <div className="slot-time">{formatTime(slot.time)}</div>
                      <div className="slot-duration">45 minutes</div>
                      {selectedSlot === slot && (
                        <div className="selected-indicator">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Contact Preferences Section */}
          <div className="form-section">
            <div className="section-header">
              <h4>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22 16.92V19.92C22 20.42 21.96 20.93 21.88 21.42C21.8 21.91 21.69 22.39 21.54 22.86C21.39 23.33 21.21 23.78 20.99 24.21C20.77 24.64 20.52 25.05 20.24 25.43C19.96 25.81 19.66 26.16 19.33 26.48C19 26.8 18.65 27.09 18.28 27.35C17.91 27.61 17.52 27.83 17.11 28.02C16.7 28.21 16.27 28.36 15.83 28.47C15.39 28.58 14.94 28.65 14.48 28.68C14.02 28.71 13.55 28.7 13.08 28.65L10.92 28.35C10.45 28.3 9.98 28.21 9.52 28.08C9.06 27.95 8.61 27.78 8.17 27.57C7.73 27.36 7.3 27.11 6.89 26.82C6.48 26.53 6.09 26.2 5.72 25.83L2.17 22.28C1.8 21.91 1.47 21.52 1.18 21.11C0.89 20.7 0.64 20.27 0.43 19.83C0.22 19.39 0.05 18.94 -0.08 18.48C-0.21 18.02 -0.3 17.55 -0.35 17.08L-0.65 14.92C-0.7 14.45 -0.71 13.98 -0.68 13.52C-0.65 13.06 -0.58 12.61 -0.47 12.17C-0.36 11.73 -0.21 11.3 -0.02 10.89C0.17 10.48 0.39 10.09 0.65 9.72C0.91 9.35 1.2 9 1.52 8.67C1.84 8.34 2.19 8.04 2.57 7.76C2.95 7.48 3.36 7.23 3.79 7.01C4.22 6.79 4.67 6.61 5.14 6.46C5.61 6.31 6.09 6.2 6.58 6.12C7.07 6.04 7.58 6 8.08 6H11.08C11.58 6 12.07 6.04 12.56 6.12C13.05 6.2 13.53 6.31 14 6.46C14.47 6.61 14.92 6.79 15.35 7.01C15.78 7.23 16.19 7.48 16.57 7.76C16.95 8.04 17.3 8.34 17.62 8.67C17.94 9 18.23 9.35 18.49 9.72C18.75 10.09 18.97 10.48 19.16 10.89C19.35 11.3 19.5 11.73 19.61 12.17C19.72 12.61 19.79 13.06 19.82 13.52C19.85 13.98 19.84 14.45 19.79 14.92L19.49 17.08C19.44 17.55 19.35 18.02 19.22 18.48C19.09 18.94 18.92 19.39 18.71 19.83C18.5 20.27 18.25 20.7 17.96 21.11C17.67 21.52 17.34 21.91 16.97 22.28L13.42 25.83C13.05 26.2 12.66 26.53 12.25 26.82C11.84 27.11 11.41 27.36 10.97 27.57C10.53 27.78 10.08 27.95 9.62 28.08C9.16 28.21 8.69 28.3 8.22 28.35L6.06 28.65C5.59 28.7 5.12 28.71 4.66 28.68C4.2 28.65 3.75 28.58 3.31 28.47C2.87 28.36 2.44 28.21 2.03 28.02C1.62 27.83 1.23 27.61 0.86 27.35C0.49 27.09 0.14 26.8 -0.19 26.48C-0.52 26.16 -0.82 25.81 -1.1 25.43C-1.38 25.05 -1.63 24.64 -1.85 24.21C-2.07 23.78 -2.25 23.33 -2.4 22.86C-2.55 22.39 -2.66 21.91 -2.74 21.42C-2.82 20.93 -2.86 20.42 -2.86 19.92V16.92C-2.86 16.42 -2.82 15.91 -2.74 15.42C-2.66 14.93 -2.55 14.45 -2.4 13.98C-2.25 13.51 -2.07 13.06 -1.85 12.63C-1.63 12.2 -1.38 11.79 -1.1 11.41C-0.82 11.03 -0.52 10.68 -0.19 10.36C0.14 10.04 0.49 9.75 0.86 9.49C1.23 9.23 1.62 9.01 2.03 8.82C2.44 8.63 2.87 8.48 3.31 8.37C3.75 8.26 4.2 8.19 4.66 8.16C5.12 8.13 5.59 8.14 6.06 8.19L8.22 8.49C8.69 8.54 9.16 8.63 9.62 8.76C10.08 8.89 10.53 9.06 10.97 9.27C11.41 9.48 11.84 9.73 12.25 10.02C12.66 10.31 13.05 10.64 13.42 11.01L16.97 14.56C17.34 14.93 17.67 15.32 17.96 15.73C18.25 16.14 18.5 16.57 18.71 17.01C18.92 17.45 19.09 17.9 19.22 18.36C19.35 18.82 19.44 19.29 19.49 19.76L19.79 21.92C19.84 22.39 19.85 22.86 19.82 23.32C19.79 23.78 19.72 24.23 19.61 24.67C19.5 25.11 19.35 25.54 19.16 25.95C18.97 26.36 18.75 26.75 18.49 27.12C18.23 27.49 17.94 27.84 17.62 28.17C17.3 28.5 16.95 28.8 16.57 29.08C16.19 29.36 15.78 29.61 15.35 29.83C14.92 30.05 14.47 30.23 14 30.38C13.53 30.53 13.05 30.64 12.56 30.72C12.07 30.8 11.58 30.84 11.08 30.84H8.08C7.58 30.84 7.07 30.8 6.58 30.72C6.09 30.64 5.61 30.53 5.14 30.38C4.67 30.23 4.22 30.05 3.79 29.83C3.36 29.61 2.95 29.36 2.57 29.08C2.19 28.8 1.84 28.5 1.52 28.17C1.2 27.84 0.91 27.49 0.65 27.12C0.39 26.75 0.17 26.36 -0.02 25.95C-0.21 25.54 -0.36 25.11 -0.47 24.67C-0.58 24.23 -0.65 23.78 -0.68 23.32C-0.71 22.86 -0.7 22.39 -0.65 21.92L-0.35 19.76C-0.3 19.29 -0.21 18.82 -0.08 18.36C0.05 17.9 0.22 17.45 0.43 17.01C0.64 16.57 0.89 16.14 1.18 15.73C1.47 15.32 1.8 14.93 2.17 14.56L5.72 11.01C6.09 10.64 6.48 10.31 6.89 10.02C7.3 9.73 7.73 9.48 8.17 9.27C8.61 9.06 9.06 8.89 9.52 8.76C9.98 8.63 10.45 8.54 10.92 8.49L13.08 8.19C13.55 8.14 14.02 8.13 14.48 8.16C14.94 8.19 15.39 8.26 15.83 8.37C16.27 8.48 16.7 8.63 17.11 8.82C17.52 9.01 17.91 9.23 18.28 9.49C18.65 9.75 19 10.04 19.33 10.36C19.66 10.68 19.96 11.03 20.24 11.41C20.52 11.79 20.77 12.2 20.99 12.63C21.21 13.06 21.39 13.51 21.54 13.98C21.69 14.45 21.8 14.93 21.88 15.42C21.96 15.91 22 16.42 22 16.92Z" stroke="currentColor" strokeWidth="2"/>
                </svg>
                How would you like to connect?
              </h4>
              <p>Choose your preferred consultation method</p>
            </div>

            <div className="contact-methods">
              <label className={`contact-method-card ${contactMethod === 'phone' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  name="contactMethod"
                  value="phone"
                  checked={contactMethod === 'phone'}
                  onChange={(e) => setContactMethod(e.target.value)}
                />
                <div className="method-content">
                  <div className="method-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M22 16.92V19.92C22.01 20.4 21.96 20.87 21.86 21.33C21.76 21.79 21.6 22.24 21.38 22.65C21.16 23.06 20.89 23.44 20.58 23.78C20.27 24.12 19.92 24.42 19.54 24.67C19.16 24.92 18.75 25.12 18.32 25.27C17.89 25.42 17.44 25.51 16.99 25.55C16.54 25.59 16.08 25.57 15.64 25.49C15.2 25.41 14.77 25.27 14.37 25.08L9.91 22.9C9.51 22.71 9.14 22.47 8.81 22.18C8.48 21.89 8.19 21.56 7.95 21.2C7.71 20.84 7.52 20.45 7.38 20.04C7.24 19.63 7.15 19.2 7.11 18.77C7.07 18.34 7.08 17.9 7.14 17.47C7.2 17.04 7.31 16.62 7.47 16.22L9.65 11.76C9.81 11.36 10.02 10.99 10.28 10.66C10.54 10.33 10.84 10.04 11.18 9.8C11.52 9.56 11.89 9.37 12.28 9.23C12.67 9.09 13.08 9 13.5 8.96C13.92 8.92 14.35 8.93 14.77 8.99C15.19 9.05 15.6 9.16 15.99 9.32L20.45 11.5C20.84 11.66 21.2 11.87 21.53 12.13C21.86 12.39 22.15 12.7 22.39 13.05C22.63 13.4 22.82 13.78 22.95 14.18C23.08 14.58 23.15 15 23.16 15.42C23.17 15.84 23.12 16.26 23.01 16.67C22.9 17.08 22.73 17.47 22.51 17.83C22.29 18.19 22.02 18.52 21.7 18.81C21.38 19.1 21.02 19.35 20.63 19.55L22 16.92Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="method-details">
                    <h5>Phone Call</h5>
                    <p>Direct phone consultation with our energy expert</p>
                    <span className="method-duration">📞 45 minutes</span>
                  </div>
                  <div className="method-check">
                    {contactMethod === 'phone' && (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </div>
                </div>
              </label>

              <label className={`contact-method-card ${contactMethod === 'video' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  name="contactMethod"
                  value="video"
                  checked={contactMethod === 'video'}
                  onChange={(e) => setContactMethod(e.target.value)}
                />
                <div className="method-content">
                  <div className="method-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M23 7L16 12L23 17V7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                  </div>
                  <div className="method-details">
                    <h5>Video Call</h5>
                    <p>Face-to-face consultation via Google Meet</p>
                    <span className="method-duration">📹 45 minutes</span>
                  </div>
                  <div className="method-check">
                    {contactMethod === 'video' && (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="form-section">
            <div className="section-header">
              <h4>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Additional Information (Optional)
              </h4>
              <p>Share any specific questions or preferences for your consultation</p>
            </div>

            <div className="form-group">
              <textarea
                id="customMessage"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                rows="4"
                placeholder="e.g., Questions about renewable energy options, specific concerns about current bills, preferred discussion topics..."
                className="custom-textarea"
              />
            </div>
          </div>

          {/* Appointment Summary */}
          {selectedSlot && (
            <div className="appointment-summary-card">
              <div className="summary-header">
                <div className="summary-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h4>Appointment Summary</h4>
              </div>

              <div className="summary-content">
                <div className="summary-item">
                  <div className="summary-label">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                      <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" strokeWidth="2"/>
                      <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" strokeWidth="2"/>
                      <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    Date & Time
                  </div>
                  <div className="summary-value">
                    {formatDate(selectedSlot.date)} at {formatTime(selectedSlot.time)}
                  </div>
                </div>

                <div className="summary-item">
                  <div className="summary-label">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                      <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    Duration
                  </div>
                  <div className="summary-value">45 minutes</div>
                </div>

                <div className="summary-item">
                  <div className="summary-label">
                    {contactMethod === 'phone' ? (
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 16.92V19.92C22.01 20.4 21.96 20.87 21.86 21.33C21.76 21.79 21.6 22.24 21.38 22.65C21.16 23.06 20.89 23.44 20.58 23.78C20.27 24.12 19.92 24.42 19.54 24.67C19.16 24.92 18.75 25.12 18.32 25.27C17.89 25.42 17.44 25.51 16.99 25.55C16.54 25.59 16.08 25.57 15.64 25.49C15.2 25.41 14.77 25.27 14.37 25.08L9.91 22.9C9.51 22.71 9.14 22.47 8.81 22.18C8.48 21.89 8.19 21.56 7.95 21.2C7.71 20.84 7.52 20.45 7.38 20.04C7.24 19.63 7.15 19.2 7.11 18.77C7.07 18.34 7.08 17.9 7.14 17.47C7.2 17.04 7.31 16.62 7.47 16.22L9.65 11.76C9.81 11.36 10.02 10.99 10.28 10.66C10.54 10.33 10.84 10.04 11.18 9.8C11.52 9.56 11.89 9.37 12.28 9.23C12.67 9.09 13.08 9 13.5 8.96C13.92 8.92 14.35 8.93 14.77 8.99C15.19 9.05 15.6 9.16 15.99 9.32L20.45 11.5C20.84 11.66 21.2 11.87 21.53 12.13C21.86 12.39 22.15 12.7 22.39 13.05C22.63 13.4 22.82 13.78 22.95 14.18C23.08 14.58 23.15 15 23.16 15.42C23.17 15.84 23.12 16.26 23.01 16.67C22.9 17.08 22.73 17.47 22.51 17.83C22.29 18.19 22.02 18.52 21.7 18.81C21.38 19.1 21.02 19.35 20.63 19.55L22 16.92Z" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    ) : (
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M23 7L16 12L23 17V7Z" stroke="currentColor" strokeWidth="2"/>
                        <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    )}
                    Contact Method
                  </div>
                  <div className="summary-value">
                    {contactMethod === 'phone' ? 'Phone Call' : 'Video Call via Google Meet'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="appointment-actions">
            <button className="btn btn-outline btn-large" onClick={onBack}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Back to Review
            </button>

            <button
              className={`btn btn-black btn-large ${!selectedSlot ? 'btn-disabled' : ''}`}
              onClick={handleBookAppointment}
              disabled={!selectedSlot || isBooking}
            >
              {isBooking ? (
                <>
                  <div className="btn-spinner"></div>
                  Booking Appointment...
                </>
              ) : (
                <>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Confirm Appointment
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentBookingStep;
