import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/clients.css';

const AddClient = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    type: 'Business',
    location: '',
    notes: '',
    energyType: 'Electricity',
    currentSupplier: '',
    monthlyConsumption: '',
    contractEndDate: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!formData.name || !formData.email) {
        showErrorMessage('VALIDATION_ERROR', 'Name and email are required');
        setLoading(false);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        showErrorMessage('VALIDATION_ERROR', 'Please enter a valid email address');
        setLoading(false);
        return;
      }

      logger.info('Adding new client:', formData);
      await brokerService.addClient(formData);
      
      showSuccessMessage('CLIENT_ADDED', 'Client added successfully!');
      navigate('/clients');
    } catch (error) {
      logger.error('Error adding client:', error);
      showErrorMessage('ADD_CLIENT_ERROR', 'Failed to add client. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/clients');
  };

  return (
    <DashboardLayout>
      <div className="clients-container">
        <div className="clients-header">
          <div className="header-content">
            <h1>
              <i className="fas fa-user-plus"></i>
              Add New Client
            </h1>
            <p>Add a new client to your portfolio and start managing their energy needs</p>
          </div>
          <div className="header-actions">
            <button className="btn-secondary" onClick={handleCancel}>
              <i className="fas fa-arrow-left"></i>
              Back to Clients
            </button>
          </div>
        </div>

        <div className="add-client-form-container">
          <form onSubmit={handleSubmit} className="add-client-form">
            <div className="form-section">
              <h3>Basic Information</h3>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Client Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter client name"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="type">Client Type</label>
                  <select
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                  >
                    <option value="Business">Business</option>
                    <option value="Individual">Individual</option>
                    <option value="Industrial">Industrial</option>
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="+33 1 23 45 67 89"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="location">Location</label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, Region"
                />
              </div>
            </div>

            <div className="form-section">
              <h3>Energy Information</h3>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="energyType">Energy Type</label>
                  <select
                    id="energyType"
                    name="energyType"
                    value={formData.energyType}
                    onChange={handleInputChange}
                  >
                    <option value="Electricity">Electricity</option>
                    <option value="Gas">Gas</option>
                    <option value="Both">Both</option>
                  </select>
                </div>
                <div className="form-group">
                  <label htmlFor="currentSupplier">Current Supplier</label>
                  <input
                    type="text"
                    id="currentSupplier"
                    name="currentSupplier"
                    value={formData.currentSupplier}
                    onChange={handleInputChange}
                    placeholder="Current energy supplier"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="monthlyConsumption">Monthly Consumption (kWh)</label>
                  <input
                    type="number"
                    id="monthlyConsumption"
                    name="monthlyConsumption"
                    value={formData.monthlyConsumption}
                    onChange={handleInputChange}
                    placeholder="1000"
                    min="0"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="contractEndDate">Contract End Date</label>
                  <input
                    type="date"
                    id="contractEndDate"
                    name="contractEndDate"
                    value={formData.contractEndDate}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3>Additional Notes</h3>
              <div className="form-group">
                <label htmlFor="notes">Notes</label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Any additional information about the client..."
                  rows="4"
                />
              </div>
            </div>

            <div className="form-actions">
              <button type="button" className="btn-secondary" onClick={handleCancel}>
                Cancel
              </button>
              <button type="submit" className="btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Adding Client...
                  </>
                ) : (
                  <>
                    <i className="fas fa-plus"></i>
                    Add Client
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AddClient;
