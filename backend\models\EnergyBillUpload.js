const mongoose = require('mongoose');

/**
 * EnergyBillUpload schema for storing energy bill upload information
 * This is used for the public energy bill upload flow (no authentication required)
 */
const energyBillUploadSchema = new mongoose.Schema({
  // Unique UUID for this upload session
  uploadUUID: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Upload status
  status: {
    type: String,
    enum: ['uploaded', 'processing', 'extracted', 'completed', 'failed'],
    default: 'uploaded'
  },
  
  // Files uploaded in this session
  files: [{
    // Original filename
    originalName: {
      type: String,
      required: true
    },
    
    // S3 key (path in the bucket)
    s3Key: {
      type: String,
      required: true
    },
    
    // S3 bucket name
    s3Bucket: {
      type: String,
      required: true
    },
    
    // File size in bytes
    fileSize: {
      type: Number,
      required: true
    },
    
    // MIME type
    mimeType: {
      type: String,
      required: true
    },
    
    // Upload timestamp
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Extracted data from OCR
  extractedData: {
    // Personal information
    firstName: String,
    lastName: String,
    email: String,
    address: String,
    
    // Energy identifiers
    deliveryPointReference: String, // PDL/PRM/RAE
    companyName: String,
    clientType: {
      type: String,
      enum: ['individual', 'professional'],
      default: 'individual'
    },
    
    // Invoice details
    invoiceData: {
      invoiceNumber: String,
      provider: String,
      amount: String,
      consumption: String,
      energyType: {
        type: String,
        enum: ['electricity', 'gas', 'both']
      },
      invoiceDate: Date,
      currency: {
        type: String,
        default: 'EUR'
      }
    },
    
    // OCR confidence score
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    
    // Extraction timestamp
    extractedAt: Date,
    
    // Whether data was manually edited by user
    userEdited: {
      type: Boolean,
      default: false
    },
    
    // User edits timestamp
    editedAt: Date
  },
  
  // Appointment booking details
  appointmentData: {
    appointmentId: String,
    calendarEventId: String,
    scheduledDate: Date,
    contactMethod: {
      type: String,
      enum: ['phone', 'video', 'in-person']
    },
    meetingLink: String,
    bookedAt: Date
  },
  
  // Client data saved to CRM
  clientData: {
    clientId: String,
    savedAt: Date
  },
  
  // Processing metadata
  processingLog: [{
    step: {
      type: String,
      enum: ['upload', 'extraction', 'data_confirmation', 'appointment_booking', 'crm_save']
    },
    status: {
      type: String,
      enum: ['started', 'completed', 'failed']
    },
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    errorDetails: mongoose.Schema.Types.Mixed
  }],
  
  // IP address for tracking
  ipAddress: String,
  
  // User agent for tracking
  userAgent: String,
  
  // Session expiry (for cleanup)
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    index: { expireAfterSeconds: 0 }
  }
}, {
  timestamps: true
});

// Indexes for performance
energyBillUploadSchema.index({ uploadUUID: 1 });
energyBillUploadSchema.index({ status: 1 });
energyBillUploadSchema.index({ createdAt: -1 });
energyBillUploadSchema.index({ expiresAt: 1 });

// Instance methods
energyBillUploadSchema.methods.addProcessingLog = function(step, status, message, errorDetails = null) {
  this.processingLog.push({
    step,
    status,
    message,
    errorDetails
  });
  return this.save();
};

energyBillUploadSchema.methods.updateStatus = function(newStatus) {
  this.status = newStatus;
  return this.save();
};

energyBillUploadSchema.methods.setExtractedData = function(extractedData, confidence) {
  this.extractedData = {
    ...extractedData,
    confidence,
    extractedAt: new Date()
  };
  this.status = 'extracted';
  return this.save();
};

energyBillUploadSchema.methods.setUserEditedData = function(editedData) {
  this.extractedData = {
    ...this.extractedData,
    ...editedData,
    userEdited: true,
    editedAt: new Date()
  };
  return this.save();
};

const EnergyBillUpload = mongoose.model('EnergyBillUpload', energyBillUploadSchema);

module.exports = EnergyBillUpload;
