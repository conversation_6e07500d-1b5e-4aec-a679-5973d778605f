import api from './api';
import logger from '../utils/logger';

class NotificationService {
  constructor() {
    this.listeners = [];
    this.notifications = [];
    this.unreadCount = 0;
  }

  // Subscribe to notification updates
  subscribe(callback) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  // Notify all subscribers
  notify() {
    this.listeners.forEach(callback => {
      callback({
        notifications: this.notifications,
        unreadCount: this.unreadCount
      });
    });
  }

  // Fetch notifications from API
  async fetchNotifications() {
    try {
      const response = await api.get('/api/admin/notifications/user');
      if (response.data.success) {
        this.notifications = response.data.data.notifications;
        this.unreadCount = response.data.data.unreadCount;
        this.notify();
        return response.data.data;
      }
    } catch (error) {
      logger.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  async markAsRead(notificationId) {
    try {
      const response = await api.patch(`/api/admin/notifications/user/${notificationId}/read`);
      if (response.data.success) {
        // Update local state
        this.notifications = this.notifications.map(notification => 
          notification._id === notificationId 
            ? { ...notification, read: true, readAt: new Date() }
            : notification
        );
        this.unreadCount = Math.max(0, this.unreadCount - 1);
        this.notify();
        return response.data.data;
      }
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllAsRead() {
    try {
      const response = await api.patch('/api/admin/notifications/user/read-all');
      if (response.data.success) {
        // Update local state
        this.notifications = this.notifications.map(notification => ({
          ...notification,
          read: true,
          readAt: new Date()
        }));
        this.unreadCount = 0;
        this.notify();
        return response.data;
      }
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Get current notifications
  getNotifications() {
    return {
      notifications: this.notifications,
      unreadCount: this.unreadCount
    };
  }

  // Start polling for new notifications (optional - for real-time updates)
  startPolling(interval = 30000) { // 30 seconds
    this.pollingInterval = setInterval(() => {
      this.fetchNotifications().catch(error => {
        logger.error('Error polling notifications:', error);
      });
    }, interval);
  }

  // Stop polling
  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  // Format notification for display
  formatNotification(notification) {
    const timeAgo = this.getTimeAgo(new Date(notification.createdAt));
    return {
      ...notification,
      timeAgo,
      formattedDate: new Date(notification.createdAt).toLocaleDateString()
    };
  }

  // Get time ago string
  getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  }

  // Get notification icon based on type
  getNotificationIcon(type) {
    switch (type) {
      case 'OfferReceived':
        return 'fas fa-tags';
      case 'ContractSigned':
        return 'fas fa-file-signature';
      case 'AppointmentScheduled':
        return 'fas fa-calendar-check';
      case 'AppointmentReminder':
        return 'fas fa-clock';
      case 'RequestStatusUpdate':
        return 'fas fa-info-circle';
      case 'DocumentVerified':
        return 'fas fa-check-circle';
      case 'PaymentReceived':
        return 'fas fa-credit-card';
      case 'SystemAlert':
        return 'fas fa-exclamation-triangle';
      case 'Other':
        return 'fas fa-bell';
      default:
        return 'fas fa-bell';
    }
  }

  // Get priority color
  getPriorityColor(priority) {
    switch (priority) {
      case 'High':
        return '#dc3545';
      case 'Normal':
        return '#007bff';
      case 'Low':
        return '#6c757d';
      default:
        return '#007bff';
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
