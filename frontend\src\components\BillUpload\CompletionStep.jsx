import React from 'react';

const CompletionStep = ({ appointmentResult, clientData, onStartOver }) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className="completion-step">
      <div className="completion-content">
        <div className="success-animation">
          <div className="success-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
            </svg>
          </div>
          <h2>All Set! 🎉</h2>
          <p className="success-message">
            Your energy bill has been analyzed and your consultation is scheduled. 
            We'll help you find the best energy deals available.
          </p>
        </div>

        <div className="completion-summary">
          <div className="summary-section">
            <h3>What Happens Next?</h3>
            <div className="next-steps">
              <div className="step-item">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>Confirmation Email</h4>
                  <p>You'll receive a confirmation email at <strong>{clientData.email}</strong> with all the details.</p>
                </div>
              </div>
              
              <div className="step-item">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>Expert Analysis</h4>
                  <p>Our energy experts will analyze your consumption patterns and find the best offers.</p>
                </div>
              </div>
              
              <div className="step-item">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>Your Consultation</h4>
                  <p>During your scheduled appointment, we'll present personalized energy solutions.</p>
                </div>
              </div>
              
              <div className="step-item">
                <div className="step-number">4</div>
                <div className="step-content">
                  <h4>Contract Assistance</h4>
                  <p>If you choose to switch, we'll handle all the paperwork and transition process.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="appointment-details-card">
            <h3>Your Appointment Details</h3>
            <div className="appointment-info">
              <div className="info-row">
                <span className="info-label">Client:</span>
                <span className="info-value">{clientData.firstName} {clientData.lastName}</span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Email:</span>
                <span className="info-value">{clientData.email}</span>
              </div>
              
              {clientData.companyName && (
                <div className="info-row">
                  <span className="info-label">Company:</span>
                  <span className="info-value">{clientData.companyName}</span>
                </div>
              )}
              
              <div className="info-row">
                <span className="info-label">Appointment ID:</span>
                <span className="info-value">{appointmentResult.appointmentId}</span>
              </div>
              
              <div className="info-row">
                <span className="info-label">Calendar Event:</span>
                <span className="info-value">{appointmentResult.calendarEventId}</span>
              </div>
            </div>
          </div>

          <div className="important-notes">
            <h3>Important Notes</h3>
            <div className="notes-list">
              <div className="note-item">
                <span className="note-icon">📧</span>
                <p>Check your email (including spam folder) for the confirmation message.</p>
              </div>
              
              <div className="note-item">
                <span className="note-icon">📞</span>
                <p>Our expert will contact you at the scheduled time using your preferred method.</p>
              </div>
              
              <div className="note-item">
                <span className="note-icon">📋</span>
                <p>Have your latest energy bills ready for the consultation.</p>
              </div>
              
              <div className="note-item">
                <span className="note-icon">🔒</span>
                <p>All your information is securely stored and GDPR compliant.</p>
              </div>
            </div>
          </div>
        </div>

        <div className="completion-actions">
          <button 
            className="btn btn-outline btn-large"
            onClick={onStartOver}
          >
            Upload Another Bill
          </button>
          
          <a 
            href="mailto:<EMAIL>" 
            className="btn btn-black btn-large"
          >
            Contact Support
          </a>
        </div>

        <div className="social-proof">
          <div className="testimonial">
            <p>"My Energy Bill helped me save €300 per year on my electricity costs. The process was simple and the expert advice was invaluable!"</p>
            <cite>— Marie D., Paris</cite>
          </div>
          
          <div className="stats">
            <div className="stat-item">
              <span className="stat-number">€450</span>
              <span className="stat-label">Average Annual Savings</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">10,000+</span>
              <span className="stat-label">Satisfied Customers</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">98%</span>
              <span className="stat-label">Success Rate</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletionStep;
