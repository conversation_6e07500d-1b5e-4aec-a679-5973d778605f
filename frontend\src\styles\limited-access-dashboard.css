/* Limited Access Dashboard - Clean Professional Design */

.limited-access-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
}

/* Clean Header */
.limited-access-dashboard .dashboard-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 28px 32px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);
}

.limited-access-dashboard .dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.limited-access-dashboard .status-message {
  margin: 0;
  font-size: 18px;
  opacity: 0.95;
  font-weight: 400;
}

/* Dashboard Grid Layout */
.limited-access-dashboard .dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Top Row - 3 Cards */
.limited-access-dashboard .top-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

/* Bottom Row - 2 Cards */
.limited-access-dashboard .bottom-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

/* Card Wrapper */
.limited-access-dashboard .card-wrapper {
  height: 100%;
}

/* Enhanced Card Styles */
.limited-access-dashboard .verification-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  height: 100%;
}

.limited-access-dashboard .verification-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #cbd5e1;
}

.limited-access-dashboard .verification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

/* Error State Styling */
.limited-access-dashboard .error-state {
  text-align: center;
  padding: 64px 32px;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7ff 100%);
  border-radius: 20px;
  border: 1px solid #fecaca;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.1);
  position: relative;
  overflow: hidden;
}

.limited-access-dashboard .error-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.limited-access-dashboard .error-state h2 {
  margin: 0 0 16px 0;
  color: #dc2626;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state p {
  margin: 0 0 32px 0;
  color: #7f1d1d;
  line-height: 1.6;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state .btn-primary {
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.limited-access-dashboard .error-state .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

/* Clean Responsive Design */
@media (max-width: 1024px) {
  .limited-access-dashboard {
    padding: 20px;
  }

  .limited-access-dashboard .top-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .limited-access-dashboard .bottom-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .limited-access-dashboard .dashboard-header {
    padding: 24px 20px;
    margin-bottom: 28px;
  }

  .limited-access-dashboard .dashboard-header h1 {
    font-size: 28px;
  }

  .limited-access-dashboard .status-message {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .limited-access-dashboard {
    padding: 16px;
  }

  .limited-access-dashboard .dashboard-header {
    padding: 20px 16px;
    margin-bottom: 24px;
    border-radius: 12px;
  }

  .limited-access-dashboard .dashboard-header h1 {
    font-size: 24px;
  }

  .limited-access-dashboard .status-message {
    font-size: 15px;
  }

  .limited-access-dashboard .dashboard-grid {
    gap: 24px;
  }

  .limited-access-dashboard .top-row,
  .limited-access-dashboard .bottom-row {
    gap: 16px;
  }

  .limited-access-dashboard .verification-card {
    border-radius: 12px;
  }
}
