/* Modern Dashboard - Professional Clean Design */

.modern-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
}

/* Modern Header - Black & White Theme */
.modern-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  color: #1f2937;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 36px;
  font-weight: 800;
  letter-spacing: -0.025em;
  color: #1e293b;
}

.header-content p {
  margin: 0;
  font-size: 18px;
  color: #64748b;
  font-weight: 500;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f1f5f9;
  border-radius: 50px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid #e2e8f0;
  color: #475569;
  white-space: nowrap;
  flex-shrink: 0;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #fbbf24;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Dashboard Container */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Cards Grid - Even Heights */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  align-items: stretch;
}

/* Modern Dashboard Cards - Consistent Heights */
.dashboard-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 320px;
}

.dashboard-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #cbd5e1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #f1f5f9;
  flex-shrink: 0;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.status-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.profile-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.steps-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.card-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.card-title p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.card-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Error State Styling */
.limited-access-dashboard .error-state {
  text-align: center;
  padding: 64px 32px;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7ff 100%);
  border-radius: 20px;
  border: 1px solid #fecaca;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.1);
  position: relative;
  overflow: hidden;
}

.limited-access-dashboard .error-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.limited-access-dashboard .error-state h2 {
  margin: 0 0 16px 0;
  color: #dc2626;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state p {
  margin: 0 0 32px 0;
  color: #7f1d1d;
  line-height: 1.6;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state .btn-primary {
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.limited-access-dashboard .error-state .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

/* Status Card Content */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-main {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-label {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.status-date {
  font-size: 14px;
  color: #6b7280;
}

.review-timeline p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Profile Card Content */
.profile-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row .label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-row .value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.profile-status {
  margin-top: 8px;
}

.status-badge-small {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge-small.complete {
  background: #dcfce7;
  color: #166534;
}

/* Steps Card Content */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
}

.step i {
  width: 20px;
  text-align: center;
}

.step.completed i {
  color: #10b981;
}

.step.active i {
  color: #f59e0b;
}

.step.pending i {
  color: #d1d5db;
}

.step span {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.step.completed span {
  color: #10b981;
}

.step.active span {
  color: #1f2937;
  font-weight: 600;
}

.step.pending span {
  color: #9ca3af;
}

/* Support Section */
.support-section {
  margin-top: 8px;
}

.support-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.support-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.support-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.support-info p {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
}

.support-btn {
  padding: 14px 28px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.support-btn:hover {
  background: linear-gradient(135deg, #4338ca, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-dashboard {
    padding: 20px;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .modern-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 28px;
    align-items: center;
  }

  .status-badge {
    align-self: center;
  }

  .support-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    padding: 16px;
  }

  .modern-header {
    padding: 24px 20px;
    border-radius: 16px;
    gap: 16px;
  }

  .header-content h1 {
    font-size: 28px;
  }

  .header-content p {
    font-size: 16px;
  }

  .status-badge {
    padding: 10px 16px;
    font-size: 13px;
  }

  .dashboard-card {
    border-radius: 16px;
    min-height: 280px;
  }

  .card-header {
    padding: 20px 20px 12px 20px;
  }

  .card-content {
    padding: 20px;
  }

  .support-card {
    padding: 24px;
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .modern-header {
    padding: 20px 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .status-badge {
    padding: 8px 12px;
    font-size: 12px;
  }
}
