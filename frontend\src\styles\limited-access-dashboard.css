/* Limited Access Dashboard Styles */

.limited-access-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
}

.limited-access-dashboard .dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 32px 24px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
  position: relative;
  overflow: hidden;
}

.limited-access-dashboard .dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.limited-access-dashboard .dashboard-header h1 {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 700;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.limited-access-dashboard .subtitle {
  margin: 0;
  font-size: 18px;
  opacity: 0.95;
  font-weight: 400;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.limited-access-dashboard .dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  align-items: start;
  margin-top: 8px;
}

.limited-access-dashboard .main-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.limited-access-dashboard .sidebar-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.limited-access-dashboard .error-state {
  text-align: center;
  padding: 64px 32px;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7ff 100%);
  border-radius: 16px;
  border: 1px solid #fecaca;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.1);
  position: relative;
  overflow: hidden;
}

.limited-access-dashboard .error-state::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.05) 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.limited-access-dashboard .error-state h2 {
  margin: 0 0 16px 0;
  color: #dc2626;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state p {
  margin: 0 0 32px 0;
  color: #7f1d1d;
  line-height: 1.6;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.limited-access-dashboard .error-state .btn-primary {
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.limited-access-dashboard .error-state .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
}

/* Enhanced Card Styles for Better Visual Hierarchy */
.limited-access-dashboard .verification-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.limited-access-dashboard .verification-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.limited-access-dashboard .verification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .limited-access-dashboard {
    max-width: 1000px;
    padding: 20px;
  }

  .limited-access-dashboard .dashboard-grid {
    gap: 28px;
  }
}

@media (max-width: 1024px) {
  .limited-access-dashboard .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .limited-access-dashboard .sidebar-column {
    order: -1;
  }

  .limited-access-dashboard .dashboard-header {
    padding: 28px 20px;
  }
}

@media (max-width: 768px) {
  .limited-access-dashboard {
    padding: 16px;
  }

  .limited-access-dashboard .dashboard-header {
    padding: 24px 16px;
    margin-bottom: 32px;
    border-radius: 12px;
  }

  .limited-access-dashboard .dashboard-header h1 {
    font-size: 28px;
  }

  .limited-access-dashboard .subtitle {
    font-size: 16px;
  }

  .limited-access-dashboard .dashboard-grid {
    gap: 20px;
  }

  .limited-access-dashboard .main-column,
  .limited-access-dashboard .sidebar-column {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .limited-access-dashboard {
    padding: 12px;
  }

  .limited-access-dashboard .dashboard-header {
    padding: 20px 12px;
    margin-bottom: 24px;
    border-radius: 8px;
  }

  .limited-access-dashboard .dashboard-header h1 {
    font-size: 24px;
  }

  .limited-access-dashboard .subtitle {
    font-size: 14px;
  }

  .limited-access-dashboard .dashboard-grid {
    gap: 16px;
  }

  .limited-access-dashboard .main-column,
  .limited-access-dashboard .sidebar-column {
    gap: 16px;
  }

  .limited-access-dashboard .verification-card {
    border-radius: 12px;
  }
}
