import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import VerificationStatusCard from './verification/VerificationStatusCard';
import WhatHappensNextCard from './verification/WhatHappensNextCard';
import ProfilePreviewCard from './verification/ProfilePreviewCard';
import SupportContactCard from './verification/SupportContactCard';
import PlatformResourcesCard from './verification/PlatformResourcesCard';
import Spinner from './Spinner';
import { showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';
import { STORAGE_KEYS, getItem } from '../utils/localStorage';
import '../styles/limited-access-dashboard.css';

const LimitedAccessDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [verificationData, setVerificationData] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // Get user data from localStorage first
      const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
      const userType = getItem(STORAGE_KEYS.USER_TYPE);
      const email = getItem(STORAGE_KEYS.EMAIL);

      if (!cognitoId) {
        logger.error('No Cognito ID found, redirecting to login');
        navigate('/login');
        return;
      }

      // Fetch detailed user data from backend
      const token = getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const response = await fetch(`${API_BASE_URL}/api/users/profile/${cognitoId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }

      const data = await response.json();
      setUserData(data.data);
      setVerificationData(data.data.verificationDetails || {});

      logger.info('Limited Access Dashboard - User data loaded:', {
        status: data.data.status,
        userType: data.data.userType,
        profileComplete: data.data.profileComplete
      });

    } catch (error) {
      logger.error('Error fetching user data:', error);
      showErrorMessage('FETCH_ERROR', 'Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileEdit = () => {
    const userType = userData?.userType?.toLowerCase();
    if (userType === 'broker') {
      navigate('/broker-info');
    } else if (userType === 'supplier') {
      navigate('/supplier-info');
    }
  };

  const handleContactSupport = () => {
    navigate('/new-ticket');
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading your dashboard..." size="large" />;
  }

  if (!userData) {
    return (
      <DashboardLayout>
        <div className="error-state">
          <h2>Unable to Load Dashboard</h2>
          <p>We couldn't load your dashboard data. Please try refreshing the page.</p>
          <button onClick={() => window.location.reload()} className="btn-primary">
            Refresh Page
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="limited-access-dashboard">
        {/* Professional Header Section */}
        <div className="dashboard-header">
          <div className="header-content">
            <div className="welcome-section">
              <h1>Welcome, {userData.userType}!</h1>
              <div className="status-badge">
                <i className="fas fa-clock"></i>
                <span>Your broker application is being reviewed</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="dashboard-content">
          <div className="content-row">
            {/* Application Status Section */}
            <div className="status-section">
              <VerificationStatusCard
                userData={userData}
                verificationData={verificationData}
                onRefresh={fetchUserData}
              />
            </div>

            {/* Profile Section */}
            <div className="profile-section">
              <ProfilePreviewCard
                userData={userData}
                onEdit={handleProfileEdit}
                canEdit={userData.status === 'Changes_Requested'}
              />
            </div>
          </div>

          {/* Additional Information Row */}
          <div className="content-row secondary">
            <div className="info-section">
              <WhatHappensNextCard
                userType={userData.userType}
                status={userData.status}
              />
            </div>

            <div className="support-section">
              <SupportContactCard
                onContactSupport={handleContactSupport}
              />
            </div>
          </div>

          {/* Resources Section */}
          <div className="content-row full-width">
            <PlatformResourcesCard
              userType={userData.userType}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default LimitedAccessDashboard;
