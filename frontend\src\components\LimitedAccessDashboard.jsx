import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import VerificationStatusCard from './verification/VerificationStatusCard';
import WhatHappensNextCard from './verification/WhatHappensNextCard';
import ProfilePreviewCard from './verification/ProfilePreviewCard';
import SupportContactCard from './verification/SupportContactCard';
import PlatformResourcesCard from './verification/PlatformResourcesCard';
import Spinner from './Spinner';
import { showErrorMessage } from '../utils/toastNotifications';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';
import { STORAGE_KEYS, getItem } from '../utils/localStorage';
import '../styles/limited-access-dashboard.css';

const LimitedAccessDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [verificationData, setVerificationData] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // Get user data from localStorage first
      const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
      const userType = getItem(STORAGE_KEYS.USER_TYPE);
      const email = getItem(STORAGE_KEYS.EMAIL);

      if (!cognitoId) {
        logger.error('No Cognito ID found, redirecting to login');
        navigate('/login');
        return;
      }

      // Fetch detailed user data from backend
      const token = getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const response = await fetch(`${API_BASE_URL}/api/users/profile/${cognitoId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }

      const data = await response.json();
      setUserData(data.data);
      setVerificationData(data.data.verificationDetails || {});

      logger.info('Limited Access Dashboard - User data loaded:', {
        status: data.data.status,
        userType: data.data.userType,
        profileComplete: data.data.profileComplete
      });

    } catch (error) {
      logger.error('Error fetching user data:', error);
      showErrorMessage('FETCH_ERROR', 'Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileEdit = () => {
    const userType = userData?.userType?.toLowerCase();
    if (userType === 'broker') {
      navigate('/broker-info');
    } else if (userType === 'supplier') {
      navigate('/supplier-info');
    }
  };

  const handleContactSupport = () => {
    navigate('/new-ticket');
  };

  if (loading) {
    return <Spinner fullScreen={true} message="Loading your dashboard..." size="large" />;
  }

  if (!userData) {
    return (
      <DashboardLayout>
        <div className="error-state">
          <h2>Unable to Load Dashboard</h2>
          <p>We couldn't load your dashboard data. Please try refreshing the page.</p>
          <button onClick={() => window.location.reload()} className="btn-primary">
            Refresh Page
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="modern-dashboard">
        {/* Modern Header */}
        <div className="modern-header">
          <div className="header-content">
            <h1>Welcome back, {userData.firstName}!</h1>
            <p>Your broker application is currently under review</p>
          </div>
          <div className="limited-dashboard-status-badge">
            <span className="status-dot"></span>
            Under Review
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="dashboard-container">
          {/* Main Cards Grid */}
          <div className="cards-grid">
            {/* Application Status Card */}
            <div className="dashboard-card status-card">
              <div className="card-header">
                <div className="card-icon status-icon">
                  <i className="fas fa-clock"></i>
                </div>
                <div className="card-title">
                  <h3>Application Status</h3>
                  <p>Current progress</p>
                </div>
              </div>
              <div className="card-content">
                <div className="status-info">
                  <div className="status-main">
                    <span className="status-label">Application Submitted</span>
                    <span className="status-date">Submitted {new Date(userData.createdAt || Date.now()).toLocaleDateString()}</span>
                  </div>
                  <div className="review-timeline">
                    <p>Typically reviewed within 2-3 business days</p>
                    <div className="progress-bar">
                      <div className="progress-fill" style={{width: '60%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Card */}
            <div className="dashboard-card profile-card">
              <div className="card-header">
                <div className="card-icon profile-icon">
                  <i className="fas fa-user"></i>
                </div>
                <div className="card-title">
                  <h3>Your Profile</h3>
                  <p>Broker information</p>
                </div>
              </div>
              <div className="card-content">
                <div className="profile-info">
                  <div className="info-row">
                    <span className="label">Name:</span>
                    <span className="value">{userData.firstName} {userData.lastName}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">Email:</span>
                    <span className="value">{userData.email}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">Phone:</span>
                    <span className="value">{userData.phone || 'Not provided'}</span>
                  </div>
                  <div className="profile-status">
                    <span className="status-badge-small complete">Profile Complete</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Next Steps Card */}
            <div className="dashboard-card next-steps-card">
              <div className="card-header">
                <div className="card-icon steps-icon">
                  <i className="fas fa-list-check"></i>
                </div>
                <div className="card-title">
                  <h3>What Happens Next?</h3>
                  <p>Review process</p>
                </div>
              </div>
              <div className="card-content">
                <div className="steps-list">
                  <div className="step completed">
                    <i className="fas fa-check"></i>
                    <span>Application submitted</span>
                  </div>
                  <div className="step active">
                    <i className="fas fa-clock"></i>
                    <span>Admin review in progress</span>
                  </div>
                  <div className="step pending">
                    <i className="fas fa-circle"></i>
                    <span>Decision notification</span>
                  </div>
                  <div className="step pending">
                    <i className="fas fa-circle"></i>
                    <span>Full platform access</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Support Section */}
          <div className="support-section">
            <div className="support-card">
              <div className="support-content">
                <div className="support-info">
                  <h3>Need Help?</h3>
                  <p>Our support team is here to assist you during the review process.</p>
                </div>
                <button className="support-btn" onClick={handleContactSupport}>
                  <i className="fas fa-headset"></i>
                  Contact Support
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default LimitedAccessDashboard;
