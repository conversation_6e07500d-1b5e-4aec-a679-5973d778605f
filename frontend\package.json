{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8080 --host", "start": "vite --port 8080 --host", "build:local": "cross-env NODE_ENV=local vite build --mode local", "build:uat": "cross-env NODE_ENV=uat vite build --mode uat", "build:linux": "NODE_ENV=development vite build", "lint": "eslint .", "preview": "vite preview --port 8080"}, "dependencies": {"autoprefixer": "^10.4.14", "aws-amplify": "^5.3.10", "buffer": "^6.0.3", "googleapis": "^150.0.1", "postcss": "^8.4.27", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-toastify": "^9.1.3", "styled-components": "^6.1.18", "tailwindcss": "^3.3.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5", "vite-plugin-node-polyfills": "^0.23.0"}}