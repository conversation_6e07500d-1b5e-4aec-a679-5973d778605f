import { useState } from 'react';
import <PERSON><PERSON>, { FormSection, FormGroup, Button, ModalFooter } from './Modal';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import { API_BASE_URL } from '../config/api-config';
import { STORAGE_KEYS, getItem } from '../utils/localStorage';

const BrokerApprovalModal = ({ broker, action, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [reason, setReason] = useState('');

  // Debug: Log the broker object to see its structure
  console.log('BrokerApprovalModal - broker object:', broker);

  const handleSubmit = async (e) => {
    console.log('handleSubmit called!', { action, broker });
    e.preventDefault();
    setLoading(true);

    try {
      console.log('Getting auth token...');
      // For admin routes, use ID_TOKEN (as per api.js interceptor)
      const token = getItem(STORAGE_KEYS.ID_TOKEN);
      console.log('Auth token:', token ? 'Found' : 'Not found');

      if (!token) {
        console.error('Authentication token not found');
        showErrorMessage('Authentication token not found');
        return;
      }

      // Check if broker and broker._id exist
      if (!broker || !broker._id) {
        console.error('Broker object or broker._id is missing:', broker);
        showErrorMessage('Broker information is missing');
        return;
      }

      console.log('All checks passed, proceeding with API call...');

      console.log('Making API call to:', `${API_BASE_URL}/api/admin/brokers/${broker._id}/verification`);
      console.log('Request payload:', {
        verificationStatus: action === 'approve' ? 'Verified' : 'Rejected',
        notes: reason.trim() || undefined
      });

      const response = await fetch(`${API_BASE_URL}/api/admin/brokers/${broker._id}/verification`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          verificationStatus: action === 'approve' ? 'Verified' : 'Rejected',
          notes: reason.trim() || undefined
        })
      });

      console.log('Response received!');
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      const data = await response.json();
      console.log('Response data:', data);

      if (!response.ok) {
        console.error('Response not ok, throwing error');
        throw new Error(data.message || `Failed to ${action} broker`);
      }

      console.log('Success! Showing success message...');
      showSuccessMessage(data.message || `Broker ${action}d successfully`);

      if (onSuccess) {
        console.log('Calling onSuccess callback...');
        onSuccess();
      }

      console.log('Closing modal...');
      onClose();
    } catch (error) {
      console.error(`Caught error in ${action}ing broker:`, error);
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      showErrorMessage(error.message || `Failed to ${action} broker`);
    } finally {
      console.log('Finally block - setting loading to false');
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={`${action === 'approve' ? 'Approve' : 'Reject'} Broker Application`}
      size="medium"
    >
      <form onSubmit={handleSubmit}>
        <FormSection>
          <div style={{ marginBottom: '20px' }}>
            <h4>Broker Details:</h4>
            <p><strong>Name:</strong> {broker.firstName} {broker.lastName}</p>
            <p><strong>Email:</strong> {broker.email}</p>
            <p><strong>Company:</strong> {broker.companyName || 'N/A'}</p>
            <p><strong>Phone:</strong> {broker.phoneNumber || 'N/A'}</p>
          </div>

          <FormGroup>
            <label htmlFor="reason">
              {action === 'approve' ? 'Approval Notes (Optional)' : 'Rejection Reason (Optional)'}
            </label>
            <textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={
                action === 'approve' 
                  ? 'Add any notes about the approval...'
                  : 'Provide a reason for rejection...'
              }
              rows={4}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '14px',
                fontFamily: 'inherit',
                resize: 'vertical'
              }}
            />
          </FormGroup>

          <div style={{ 
            padding: '15px', 
            backgroundColor: action === 'approve' ? '#d4edda' : '#f8d7da',
            border: `1px solid ${action === 'approve' ? '#c3e6cb' : '#f5c6cb'}`,
            borderRadius: '4px',
            marginTop: '15px'
          }}>
            <p style={{ 
              margin: 0, 
              color: action === 'approve' ? '#155724' : '#721c24',
              fontSize: '14px'
            }}>
              <strong>Note:</strong> An email notification will be sent to the broker informing them of your decision.
            </p>
          </div>
        </FormSection>

        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant={action === 'approve' ? 'primary' : 'danger'}
            disabled={loading}
          >
            {loading ? 'Processing...' : (action === 'approve' ? 'Approve Broker' : 'Reject Broker')}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default BrokerApprovalModal;
