import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import DashboardStats from '../components/DashboardStats';
import DashboardOverview from '../components/DashboardOverview';
import DashboardLayout from '../components/DashboardLayout';
import SupplierDashboard from './SupplierDashboard';
import BrokerDashboard from './BrokerDashboard';
import AdminDashboard from './AdminDashboard';
import LimitedAccessDashboard from '../components/LimitedAccessDashboard';
import '../styles/app.css';
import '../styles/dashboard-content.css';
import Spinner from '../components/Spinner';
import { trackNavigation, getStoredNavigationHistory } from '../utils/navigationTracker';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config/api-config';
import { STORAGE_KEYS, getItem } from '../utils/localStorage';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // User type and info states
  const [userType, setUserType] = useState(null);
  const [userStatus, setUserStatus] = useState(null);
  const [professionalInfoComplete, setProfessionalInfoComplete] = useState(false);
  const [professionalInfo, setProfessionalInfo] = useState(null);
  const [individualInfoComplete, setIndividualInfoComplete] = useState(false);
  const [individualInfo, setIndividualInfo] = useState(null);
  const [welcomeMessage, setWelcomeMessage] = useState('');

  // Debug navigation history when dashboard mounts
  useEffect(() => {
    // Track dashboard mount
    trackNavigation(
      'previous-page',
      '/dashboard',
      'component-mount',
      {
        component: 'Dashboard',
        locationState: location.state ? 'present' : 'absent',
        referrer: document.referrer
      }
    );

    // Log navigation history
    const navigationHistory = getStoredNavigationHistory();
    logger.debug('Dashboard - Navigation History:', navigationHistory);

    logger.info('Dashboard mounted');

    // Clear the first-time flow flag when dashboard loads
    // This ensures that on subsequent logins, the user goes directly to dashboard
    if (localStorage.getItem('first_time_flow') === 'true') {
      logger.debug('Dashboard - Clearing first-time flow flag');
      localStorage.removeItem('first_time_flow');

      // Set the flag indicating the user has completed the first-time flow
      // This ensures that on subsequent logins, they go directly to dashboard
      localStorage.setItem('firstTimeFlowCompleted', 'true');
      logger.debug('Dashboard - Set firstTimeFlowCompleted flag to true');
    }

    // Check if the user has uploaded their first invoice
    const firstInvoiceUploaded = localStorage.getItem('firstInvoiceUploaded') === 'true';
    logger.debug('Dashboard - First invoice uploaded:', firstInvoiceUploaded);

    // Debug localStorage state
    logger.debug('Dashboard - localStorage state:', {
      userType: localStorage.getItem('userType'),
      individualInfoCompleted: localStorage.getItem('individualInfoCompleted'),
      professionalInfoCompleted: localStorage.getItem('professionalInfoCompleted'),
      isAuthenticated: localStorage.getItem('isAuthenticated'),
      individualInfo: localStorage.getItem('individualInfo') ? 'present' : 'absent',
      professionalInfo: localStorage.getItem('professionalInfo') ? 'present' : 'absent',
      firstInvoiceUploaded: firstInvoiceUploaded
    });
  }, []);

  // Check for user type and welcome message from location state
  useEffect(() => {
    logger.debug('Dashboard - location effect triggered');

    // Check for message from location state
    if (location.state?.message) {
      setWelcomeMessage(location.state.message);
      // Clear the message from location state after displaying it
      window.history.replaceState({}, document.title);
      logger.debug('Found message in location state:', location.state.message);
    }

    // Try to get user type from localStorage
    const storedUserType = localStorage.getItem('userType');
    logger.info('🔍 Dashboard - User type from localStorage:', storedUserType);
    logger.info('🔍 Dashboard - All localStorage keys:', Object.keys(localStorage));
    logger.info('🔍 Dashboard - UserData from localStorage:', localStorage.getItem('userData'));

    // If localStorage is empty, redirect to login
    // This will trigger a fresh authentication and restore the correct data from Cognito
    if (!storedUserType) {
      logger.warn('Dashboard - No user type found in localStorage! Redirecting to login');

      // Redirect to login page
      navigate('/login', {
        state: {
          message: 'Please log in again to restore your session data.'
        }
      });
      return;
    }

    // We already checked for storedUserType above, so we know it exists at this point
    // Set the user type in the component state
    setUserType(storedUserType);

    // PRIORITY: Admin users bypass all profile completion checks
    if (storedUserType.toLowerCase() === 'admin') {
      logger.info('🔑 ADMIN USER DETECTED in Dashboard - No profile checks needed');
      setLoading(false);
      return;
    }

    // For brokers and suppliers, check verification status
    if (['broker', 'supplier'].includes(storedUserType.toLowerCase())) {
      checkUserVerificationStatus(storedUserType);
      return;
    }

    // For suppliers, check if profile is complete
    if (storedUserType === 'supplier') {
      // Check if profile is completed using the profileCompletion flag from Cognito
      const isProfileComplete = localStorage.getItem('profileCompletion') === 'true';
      setProfessionalInfoComplete(isProfileComplete);
      logger.debug('Dashboard - Supplier info complete (from profileCompletion):', isProfileComplete);

      if (!isProfileComplete) {
        // Check if we're already on the supplier info page to avoid redirect loops
        const currentPath = window.location.pathname;
        if (currentPath !== '/supplier-info') {
          logger.info('Dashboard - Supplier profile not complete, redirecting to supplier info page');
          // Redirect to supplier info page if profile is not complete
          navigate('/supplier-info');
          return;
        } else {
          logger.info('Dashboard - Already on supplier info page, not redirecting');
        }
      }
    }
    // For brokers, check if profile is complete
    else if (storedUserType === 'broker') {
      // Check if profile is completed using the profileCompletion flag from Cognito
      const isProfileComplete = localStorage.getItem('profileCompletion') === 'true';
      setProfessionalInfoComplete(isProfileComplete);
      logger.debug('Dashboard - Broker info complete (from profileCompletion):', isProfileComplete);

      if (!isProfileComplete) {
        // Check if we're already on the broker info page to avoid redirect loops
        const currentPath = window.location.pathname;
        if (currentPath !== '/broker-info') {
          logger.info('Dashboard - Broker profile not complete, redirecting to broker info page');
          // Redirect to broker info page if profile is not complete
          navigate('/broker-info');
          return;
        } else {
          logger.info('Dashboard - Already on broker info page, not redirecting');
        }
      }
    }
    // If professional, check if info is complete
    else if (storedUserType === 'professional') {
      // Check if profile is completed using the profileCompletion flag from Cognito
      const isProfileComplete = localStorage.getItem('profileCompletion') === 'true';
      setProfessionalInfoComplete(isProfileComplete);
      logger.debug('Dashboard - Professional info complete (from profileCompletion):', isProfileComplete);

      if (isProfileComplete) {
        // Fetch professional info from API
        try {
          // Get the Cognito ID from localStorage
          const cognitoId = localStorage.getItem('cognitoId');

          if (cognitoId) {
            logger.debug('Dashboard - Fetching professional info from API for Cognito ID:', cognitoId);

            // Fetch user data from API
            fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`)
              .then(response => {
                if (!response.ok) {
                  throw new Error('Failed to fetch user data');
                }
                return response.json();
              })
              .then(userData => {
                logger.debug('Dashboard - Professional info fetched successfully:', userData);
                logger.debug('Dashboard - Professional profile data:', userData.data?.profile);
                setProfessionalInfo(userData);
              })
              .catch(error => {
                logger.error('Dashboard - Error fetching professional info from API:', error);
              });
          } else {
            logger.error('Dashboard - No Cognito ID found in localStorage');
          }
        } catch (error) {
          logger.error('Dashboard - Error fetching professional info:', error);
        }
      } else {
        logger.info('Dashboard - Professional profile not complete, redirecting to professional info page');
        // Redirect to professional info page if profile is not complete
        navigate('/professional-info');
      }
    }
    // If individual, check if info is complete
    else if (storedUserType === 'individual') {
      // Check if profile is completed using the profileCompletion flag from Cognito
      const isProfileComplete = localStorage.getItem('profileCompletion') === 'true';
      setIndividualInfoComplete(isProfileComplete);
      logger.debug('Dashboard - Individual info complete (from profileCompletion):', isProfileComplete);

      if (isProfileComplete) {
        // Fetch individual info from API
        try {
          // Get the Cognito ID from localStorage
          const cognitoId = localStorage.getItem('cognitoId');

          if (cognitoId) {
            logger.debug('Dashboard - Fetching individual info from API for Cognito ID:', cognitoId);

            // Fetch user data from API
            fetch(`${API_BASE_URL}/api/users/cognito/${cognitoId}`)
              .then(response => {
                if (!response.ok) {
                  throw new Error('Failed to fetch user data');
                }
                return response.json();
              })
              .then(userData => {
                logger.debug('Dashboard - Individual info fetched successfully:', userData);
                logger.debug('Dashboard - Individual profile data:', userData.data?.profile);
                setIndividualInfo(userData);
              })
              .catch(error => {
                logger.error('Dashboard - Error fetching individual info from API:', error);
              });
          } else {
            logger.error('Dashboard - No Cognito ID found in localStorage');
          }
        } catch (error) {
          logger.error('Dashboard - Error fetching individual info:', error);
        }
      } else {
        logger.info('Dashboard - Individual profile not complete, redirecting to individual info page');
        // Redirect to individual info page if profile is not complete
        navigate('/individual-info');
      }
    } else {
      // If no user type is set, redirect to user type selection
      logger.info('Dashboard - No user type found, redirecting to user type selection');
      setLoading(false);
      navigate('/user-type');
    }

    // Ensure loading is set to false after a timeout
    setTimeout(() => {
      setLoading(false);
    }, 500);
  }, [location, navigate]);

  // Check user verification status for brokers and suppliers
  const checkUserVerificationStatus = async (userType) => {
    try {
      const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
      const token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!cognitoId || !token) {
        logger.error('Missing authentication data, redirecting to login');
        navigate('/login');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/users/profile/${cognitoId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user status');
      }

      const data = await response.json();
      const user = data.data;

      setUserStatus(user.status);
      logger.info('🔍 DASHBOARD DEBUG - User verification status:', {
        status: user.status,
        profileComplete: user.profileComplete,
        userType: user.userType,
        verificationStatus: user.verificationStatus
      });

      // Additional debugging for dashboard routing
      console.log('🎯 DASHBOARD ROUTING DEBUG:', {
        userStatus: user.status,
        userType: userType,
        limitedAccessStatuses: ['Profile_Submitted', 'Under_Review', 'Changes_Requested', 'Pending'],
        fullAccessStatuses: ['Approved', 'Active'],
        shouldShowLimited: ['Profile_Submitted', 'Under_Review', 'Changes_Requested', 'Pending'].includes(user.status),
        shouldShowFull: ['Approved', 'Active'].includes(user.status)
      });

      // Check if user needs limited access dashboard
      const limitedAccessStatuses = ['Profile_Submitted', 'Under_Review', 'Changes_Requested', 'Pending'];
      if (limitedAccessStatuses.includes(user.status) && ['broker', 'supplier'].includes(userType.toLowerCase())) {
        logger.info('Broker/Supplier has limited access status, showing limited dashboard');
        setLoading(false);
        return;
      }

      // Check if user needs to complete profile
      if (!user.profileComplete && user.status === 'Pending') {
        logger.info('User needs to complete profile, redirecting to profile completion');
        if (userType.toLowerCase() === 'broker') {
          navigate('/broker-info');
        } else if (userType.toLowerCase() === 'supplier') {
          navigate('/supplier-info');
        }
        return;
      }

      // User has full access
      setLoading(false);

    } catch (error) {
      logger.error('Error checking user verification status:', error);
      setLoading(false);
    }
  };

  // Handle edit profile - now redirects to the profile page
  const handleEditProfile = () => {
    navigate('/profile');
  };

  // Render specific dashboard based on user type
  const renderDashboard = () => {
    if (loading) {
      return <Spinner fullScreen={true} message="Loading your dashboard..." size="large" />;
    }

    // Check if user needs limited access dashboard (for brokers/suppliers awaiting verification)
    const normalizedUserType = userType?.toLowerCase();
    const limitedAccessStatuses = ['Profile_Submitted', 'Under_Review', 'Changes_Requested', 'Pending'];
    const fullAccessStatuses = ['Approved', 'Active']; // Both Approved and Active grant full access

    if (['broker', 'supplier'].includes(normalizedUserType)) {
      console.log('🚀 BROKER/SUPPLIER DASHBOARD ROUTING:', {
        userType: normalizedUserType,
        userStatus: userStatus,
        limitedAccessStatuses: limitedAccessStatuses,
        fullAccessStatuses: fullAccessStatuses,
        isLimitedAccess: limitedAccessStatuses.includes(userStatus),
        isFullAccess: fullAccessStatuses.includes(userStatus)
      });

      if (limitedAccessStatuses.includes(userStatus)) {
        logger.info('🔒 Rendering Limited Access Dashboard for status:', userStatus);
        return <LimitedAccessDashboard />;
      } else if (fullAccessStatuses.includes(userStatus)) {
        logger.info('🎉 Broker/Supplier has full access status:', userStatus);
        // Continue to render full dashboard below
      } else {
        logger.warn('❓ Unknown status for broker/supplier:', userStatus);
        console.log('❓ UNKNOWN STATUS - Defaulting to Limited Access Dashboard');
        return <LimitedAccessDashboard />;
      }
    }

    // Route to specific dashboards based on user type (case-insensitive)
    switch (normalizedUserType) {
      case 'admin':
        logger.info('Rendering Admin Dashboard for userType:', userType);
        return <AdminDashboard />;

      case 'supplier':
        logger.info('Rendering Supplier Dashboard');
        return <SupplierDashboard />;

      case 'broker':
        logger.info('🎯 Rendering Broker Dashboard for approved broker');
        console.log('🎯 BROKER DASHBOARD - About to render BrokerDashboard component');
        return <BrokerDashboard />;

      case 'professional':
      case 'individual':
      default:
        logger.info('Rendering Default Dashboard for user type:', userType);
        return (
          <DashboardLayout>
            <div className="dashboard-content">
              {/* Welcome message if present */}
              {welcomeMessage && (
                <div className="welcome-message">
                  {welcomeMessage}
                </div>
              )}

              {/* Dashboard Stats Cards */}
              <DashboardStats />

              {/* Dashboard Overview - Shows recent invoices, contracts, and appointments */}
              <DashboardOverview />
            </div>
          </DashboardLayout>
        );
    }
  };

  return renderDashboard();
};

export default Dashboard;
