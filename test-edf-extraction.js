// Test script to see what text is being extracted from the EDF bill
const fs = require('fs');
const path = require('path');

// Simulate the text lines that would be extracted from the EDF bill
const mockEdfTextLines = [
  "NOUS CONTACTER",
  "N° client : 5 016 040 364",
  "Identifiant Internet : <EMAIL>",
  "Par Internet",
  "edf.fr",
  "application mobile : EDF & MOI",
  "mail : <EMAIL>",
  "Par téléphone",
  "Du lundi au samedi dès 8h et jusqu'à 20h",
  "3404",
  "(Service gratuit + prix appel)",
  "Mon Compte Sur Serveur Vocal",
  "09 70 83 33 33",
  "(Service gratuit + prix appel)",
  "Par courrier",
  "EDF SERVICE CLIENTS TSA 21941",
  "62978 ARRAS CEDEX 9",
  "Urgence dépannage Electricité (Enedis)",
  "09/26/75035 (Service gratuit + prix appel)",
  "NOUS ADRESSER VOTRE CHEQUE ENERGIE",
  "Par internet",
  "www.chequeenergie.gouv.fr",
  "Par courrier",
  "EDF TSA 81401",
  "87014 LIMOGES Cedex 1",
  "Lieu de consommation",
  "PAV LOT 35",
  "LOT LA VALLEE",
  "Mme THEBAULT ELODIE",
  "LOT LA VALLEE",
  "2 IMPASSE DU RUISSEAU",
  "35490 SENS DE BRETAGNE",
  "Facture du 10/01/2024",
  "N° 32 115 643 865",
  "Electricité (relevé Enedis)",
  "2 380,40 €",
  "TVA",
  "448,92 €",
  "Paiements déjà effectués",
  "-2 758,74 €",
  "Facture TTC",
  "70,58 €",
  "Montant total",
  "70,58 €",
  "TTC",
  "Prélève le",
  "08/02/2024",
  "Les prochaines étapes",
  "Prochaine facture vers le 09/01/2025"
];

// Import the extraction function
const { extractClientDataFromTextLines } = require('./backend/routes/energyBills');

console.log('🧪 Testing EDF bill extraction with mock data...');
console.log('📝 Mock text lines:', mockEdfTextLines.length, 'lines');

try {
  const result = extractClientDataFromTextLines(mockEdfTextLines);
  console.log('\n📋 Extraction Result:');
  console.log(JSON.stringify(result, null, 2));
  
  console.log('\n✅ Expected vs Actual:');
  console.log('Expected Client Type: individual');
  console.log('Actual Client Type:', result.clientType, result.clientType === 'individual' ? '✅' : '❌');
  console.log('Expected Name: THEBAULT ELODIE');
  console.log('Actual Name:', result.fullName || result.companyName, (result.fullName === 'THEBAULT ELODIE') ? '✅' : '❌');
  console.log('Expected PDL: 5016040364');
  console.log('Actual PDL:', result.pdlPrmRae, (result.pdlPrmRae === '5016040364') ? '✅' : '❌');
  
} catch (error) {
  console.error('❌ Error during extraction:', error);
}
