/* Broker Dashboard - Clean Professional Design */

.broker-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f8fafc;
  min-height: calc(100vh - 60px);
}

/* Header Section */
.broker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 28px 32px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);
}

.broker-header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.broker-header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.broker-header-actions {
  display: flex;
  gap: 12px;
}

.btn-broker-action {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.btn-broker-action.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-broker-action.primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Stats Grid */
.broker-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.clients::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.deals::before {
  background: linear-gradient(90deg, #10b981, #047857);
}

.stat-card.commission::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.conversion::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.stat-card.clients .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card.deals .stat-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.stat-card.commission .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card.conversion .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.stat-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.content-row.single {
  grid-template-columns: 1fr;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title i {
  color: #4f46e5;
  font-size: 18px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.view-all-btn {
  padding: 8px 16px;
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.card-body {
  padding: 24px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .broker-dashboard {
    padding: 16px;
  }
  
  .broker-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px;
  }
  
  .broker-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .content-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .broker-header-content h1 {
    font-size: 28px;
  }
}
