/* Modern Broker Dashboard Styles */
.broker-dashboard {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f8fafc;
  min-height: 100vh;
}

.broker-dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Welcome Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
  box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.header-actions .btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.header-actions .btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.clients {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.deals {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.conversion {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.stat-trend.positive {
  background: #dcfce7;
  color: #16a34a;
}

.stat-trend.negative {
  background: #fef2f2;
  color: #dc2626;
}

.stat-content h3 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.stat-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.card-header h3 i {
  color: #667eea;
}

.btn-link {
  color: #667eea;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: none;
  background: none;
  cursor: pointer;
}

.btn-link:hover {
  background: #e0e7ff;
  color: #4338ca;
}

.card-content {
  padding: 24px;
}

/* Client List */
.client-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.client-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.client-item:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.client-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 10px;
  font-size: 16px;
}

.client-info {
  flex: 1;
}

.client-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.client-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.client-meta {
  font-size: 0.75rem;
  color: #9ca3af;
}

.client-value {
  text-align: right;
}

.client-value .value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #059669;
  display: block;
}

.client-value .period {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Deal List */
.deal-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.deal-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.deal-item:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.deal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 10px;
  font-size: 16px;
}

.deal-info {
  flex: 1;
}

.deal-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.deal-info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.deal-date {
  font-size: 0.75rem;
  color: #9ca3af;
}

.deal-value {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.deal-value .commission {
  font-size: 1.125rem;
  font-weight: 700;
  color: #059669;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.pending {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.active {
  background: #dbeafe;
  color: #2563eb;
}

/* Quick Actions */
.quick-actions {
  grid-column: 1 / -1;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #374151;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 20px;
  color: #667eea;
}

.action-btn.primary .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.action-btn span {
  font-size: 0.875rem;
  font-weight: 600;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: #f3f4f6;
  border-radius: 16px;
  margin: 0 auto 16px;
  font-size: 24px;
  color: #9ca3af;
}

.empty-state h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 0.875rem;
  margin: 0 0 20px 0;
}

/* Performance Section */
.performance {
  grid-column: 1 / -1;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.metric-item {
  text-align: center;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.metric-label {
  display: block;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  display: block;
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.metric-change.positive {
  background: #dcfce7;
  color: #16a34a;
}

.metric-change.negative {
  background: #fef2f2;
  color: #dc2626;
}

/* Pipeline Section */
.pipeline-section {
  margin-top: 32px;
}

.pipeline {
  grid-column: 1 / -1;
}

.pipeline-stages {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.pipeline-stage {
  text-align: center;
  flex: 1;
}

.stage-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stage-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.pipeline-arrow {
  color: #cbd5e1;
  font-size: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .broker-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .pipeline-stages {
    flex-direction: column;
    gap: 20px;
  }

  .pipeline-arrow {
    transform: rotate(90deg);
  }
}
