import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/broker-dashboard.css';

const BrokerDashboard = () => {
  console.log('🎯 BROKER DASHBOARD COMPONENT - Component is rendering!');
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeClients, setActiveClients] = useState([]);
  const [recentDeals, setRecentDeals] = useState([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    activeDeals: 0,
    monthlyCommission: 0,
    conversionRate: 0
  });

  useEffect(() => {
    console.log('🎯 BROKER DASHBOARD COMPONENT - useEffect triggered');
    console.log('🎯 BROKER DASHBOARD COMPONENT - About to fetch dashboard data');
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🎯 BROKER DASHBOARD - fetchDashboardData started');
      setLoading(true);
      logger.info('Fetching broker dashboard data');

      // Fetch dashboard stats
      console.log('🎯 BROKER DASHBOARD - Calling brokerService.getDashboardStats()');
      const statsResponse = await brokerService.getDashboardStats();
      console.log('🎯 BROKER DASHBOARD - brokerService response:', statsResponse);

      if (statsResponse.success) {
        console.log('🎯 BROKER DASHBOARD - Setting stats data:', statsResponse.data);
        setStats(statsResponse.data);
        // Set real data from API
        setActiveClients(statsResponse.data.recentClients || []);
        setRecentDeals(statsResponse.data.recentDeals || []);
      }

      logger.info('Broker dashboard data loaded successfully');
      console.log('🎯 BROKER DASHBOARD - Data loaded successfully');
    } catch (error) {
      logger.error('Error fetching broker dashboard data:', error);
      console.error('🎯 BROKER DASHBOARD - Error:', error);
      showErrorMessage('DATA_LOAD_FAILED');

      // Fallback to empty data
      setStats({
        totalClients: 0,
        activeDeals: 0,
        monthlyCommission: 0,
        conversionRate: 0
      });
      setActiveClients([]);
      setRecentDeals([]);
    } finally {
      setLoading(false);
      console.log('🎯 BROKER DASHBOARD - Loading set to false');
    }
  };

  const handleAddClient = () => {
    navigate('/add-client');
  };

  const handleViewAllClients = () => {
    navigate('/clients');
  };

  const handleViewAllDeals = () => {
    navigate('/deals');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="broker-dashboard-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading broker dashboard..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="broker-dashboard-container">
        {/* Stats Cards */}
        <div className="broker-stats-container">
          <div className="broker-stats-grid">
            <div className="broker-stat-card">
              <div className="broker-stat-card-content">
                <div className="broker-stat-card-icon">
                  <i className="fas fa-users"></i>
                </div>
                <div className="broker-stat-card-title">Total Clients</div>
                <div className="broker-stat-card-description">Manage your clients and energy deals</div>
              </div>
              <div className="broker-stat-card-value">{stats.totalClients}</div>
            </div>

            <div className="broker-stat-card">
              <div className="broker-stat-card-content">
                <div className="broker-stat-card-icon">
                  <i className="fas fa-handshake"></i>
                </div>
                <div className="broker-stat-card-title">Active Deals</div>
                <div className="broker-stat-card-description">Current active energy deals</div>
              </div>
              <div className="broker-stat-card-value">{stats.activeDeals}</div>
            </div>

            <div className="broker-stat-card">
              <div className="broker-stat-card-content">
                <div className="broker-stat-card-icon">
                  <i className="fas fa-euro-sign"></i>
                </div>
                <div className="broker-stat-card-title">Monthly Commission</div>
                <div className="broker-stat-card-description">Commission earned this month</div>
              </div>
              <div className="broker-stat-card-value">€{stats.monthlyCommission.toLocaleString()}</div>
            </div>

            <div className="broker-stat-card">
              <div className="broker-stat-card-content">
                <div className="broker-stat-card-icon">
                  <i className="fas fa-percentage"></i>
                </div>
                <div className="broker-stat-card-title">Conversion Rate</div>
                <div className="broker-stat-card-description">Deal conversion percentage</div>
              </div>
              <div className="broker-stat-card-value">{stats.conversionRate}%</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="dashboard-content-grid">
          {/* Active Clients */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-users"></i>
                Active Clients
              </h3>
              <button className="btn-link" onClick={handleViewAllClients}>
                View All
              </button>
            </div>
            <div className="card-content">
              {activeClients.length > 0 ? (
                <div className="clients-list">
                  {activeClients.slice(0, 5).map((client) => (
                    <div key={client.id} className="client-item">
                      <div className="client-info">
                        <h4>{client.name}</h4>
                        <p>{client.type} • {client.location}</p>
                        <span className="client-status">
                          {client.activeContracts} active contracts
                        </span>
                      </div>
                      <div className="client-value">
                        <span className="value">€{client.monthlyValue}</span>
                        <span className="period">/month</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-users"></i>
                  <p>No active clients</p>
                  <button className="btn-primary" onClick={handleAddClient}>
                    Add Your First Client
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Deals */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-handshake"></i>
                Recent Deals
              </h3>
              <button className="btn-link" onClick={handleViewAllDeals}>
                View All
              </button>
            </div>
            <div className="card-content">
              {recentDeals.length > 0 ? (
                <div className="deals-list">
                  {recentDeals.slice(0, 5).map((deal) => (
                    <div key={deal.id} className="deal-item">
                      <div className="deal-info">
                        <h4>{deal.clientName}</h4>
                        <p>{deal.supplier} • {deal.energyType}</p>
                        <span className="deal-date">
                          {new Date(deal.closedDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="deal-commission">
                        <span className="commission">€{deal.commission}</span>
                        <span className={`status-badge ${deal.status.toLowerCase()}`}>
                          {deal.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-handshake"></i>
                  <p>No recent deals</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="dashboard-card quick-actions-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-bolt"></i>
                Quick Actions
              </h3>
            </div>
            <div className="card-content">
              <div className="quick-actions">
                <button className="action-btn primary-action" onClick={handleAddClient}>
                  <i className="fas fa-user-plus"></i>
                  <span>Add New Client</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/compare-offers')}>
                  <i className="fas fa-balance-scale"></i>
                  <span>Compare Offers</span>
                </button>
                <button className="action-btn" onClick={handleViewAnalytics}>
                  <i className="fas fa-chart-line"></i>
                  <span>View Analytics</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/profile')}>
                  <i className="fas fa-cog"></i>
                  <span>Settings</span>
                </button>
              </div>
            </div>
          </div>

          {/* Commission Overview */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-chart-bar"></i>
                Commission Overview
              </h3>
            </div>
            <div className="card-content">
              <div className="commission-metrics">
                <div className="metric">
                  <span className="metric-label">This Month</span>
                  <span className="metric-value">€{stats.monthlyCommission}</span>
                </div>
                <div className="metric">
                  <span className="metric-label">Last Month</span>
                  <span className="metric-value">€2,450</span>
                </div>
                <div className="metric">
                  <span className="metric-label">YTD Total</span>
                  <span className="metric-value">€18,750</span>
                </div>
              </div>
            </div>
          </div>

          {/* Pipeline Overview */}
          <div className="dashboard-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-funnel-dollar"></i>
                Sales Pipeline
              </h3>
            </div>
            <div className="card-content">
              <div className="pipeline-stages">
                <div className="stage">
                  <span className="stage-label">Prospects</span>
                  <span className="stage-count">{stats?.pipelineData?.prospects || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Qualified</span>
                  <span className="stage-count">{stats?.pipelineData?.qualified || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Proposal</span>
                  <span className="stage-count">{stats?.pipelineData?.proposal || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Closing</span>
                  <span className="stage-count">{stats?.pipelineData?.closing || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrokerDashboard;
