import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/broker-dashboard.css';

const BrokerDashboard = () => {
  console.log('🎯 BROKER DASHBOARD COMPONENT - Component is rendering!');
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeClients, setActiveClients] = useState([]);
  const [recentDeals, setRecentDeals] = useState([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    activeDeals: 0,
    monthlyCommission: 0,
    conversionRate: 0
  });

  useEffect(() => {
    console.log('🎯 BROKER DASHBOARD COMPONENT - useEffect triggered');
    console.log('🎯 BROKER DASHBOARD COMPONENT - About to fetch dashboard data');
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🎯 BROKER DASHBOARD - fetchDashboardData started');
      setLoading(true);
      logger.info('Fetching broker dashboard data');

      // Fetch all data in parallel for better performance
      const [statsResponse, clientsResponse, dealsResponse] = await Promise.all([
        brokerService.getDashboardStats(),
        brokerService.getActiveClients(),
        brokerService.getRecentDeals()
      ]);

      console.log('🎯 BROKER DASHBOARD - All responses received:', {
        stats: statsResponse,
        clients: clientsResponse,
        deals: dealsResponse
      });

      // Set stats data
      if (statsResponse.success) {
        setStats(statsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Stats set:', statsResponse.data);
      }

      // Set clients data
      if (clientsResponse.success) {
        setActiveClients(clientsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Clients set:', clientsResponse.data.length, 'clients');
      }

      // Set deals data
      if (dealsResponse.success) {
        setRecentDeals(dealsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Deals set:', dealsResponse.data.length, 'deals');
      }

      logger.info('Broker dashboard data loaded successfully');
      console.log('🎯 BROKER DASHBOARD - Data loaded successfully');
    } catch (error) {
      logger.error('Error fetching broker dashboard data:', error);
      console.error('🎯 BROKER DASHBOARD - Error:', error);
      showErrorMessage('DATA_LOAD_FAILED');

      // Fallback to empty data
      setStats({
        totalClients: 0,
        activeDeals: 0,
        monthlyCommission: 0,
        conversionRate: 0
      });
      setActiveClients([]);
      setRecentDeals([]);
    } finally {
      setLoading(false);
      console.log('🎯 BROKER DASHBOARD - Loading set to false');
    }
  };

  const handleAddClient = () => {
    navigate('/add-client');
  };

  const handleViewAllClients = () => {
    navigate('/clients');
  };

  const handleViewAllDeals = () => {
    navigate('/deals');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="broker-dashboard-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading broker dashboard..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="broker-dashboard">
        {/* Dashboard Header */}
        <div className="broker-header">
          <div className="broker-header-content">
            <h1>Broker Dashboard</h1>
            <p>Manage your clients, track deals, and monitor your commission earnings</p>
          </div>
          <div className="broker-header-actions">
            <button className="btn-broker-action primary" onClick={handleAddClient}>
              <i className="fas fa-plus"></i>
              Add New Client
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="broker-stats-grid">
          <div className="stat-card clients">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : stats.totalClients}</h3>
              <p>Total Clients</p>
            </div>
          </div>

          <div className="stat-card deals">
            <div className="stat-icon">
              <i className="fas fa-handshake"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : stats.activeDeals}</h3>
              <p>Active Deals</p>
            </div>
          </div>

          <div className="stat-card commission">
            <div className="stat-icon">
              <i className="fas fa-euro-sign"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : `€${stats.monthlyCommission.toLocaleString()}`}</h3>
              <p>Monthly Commission</p>
            </div>
          </div>

          <div className="stat-card conversion">
            <div className="stat-icon">
              <i className="fas fa-percentage"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : `${stats.conversionRate}%`}</h3>
              <p>Conversion Rate</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="dashboard-content">
          {/* Top Row - Client & Deals Overview */}
          <div className="content-row">
            {/* Active Clients */}
            <div className="content-card">
              <div className="card-header">
                <div className="header-title">
                  <i className="fas fa-users"></i>
                  <h3>Active Clients</h3>
                </div>
                <button className="view-all-btn" onClick={handleViewAllClients}>
                  View All
                </button>
              </div>
              <div className="card-body">
                {loading ? (
                  <div className="loading-state">
                    <i className="fas fa-spinner fa-spin"></i>
                    <p>Loading clients...</p>
                  </div>
                ) : activeClients.length > 0 ? (
                  <div className="client-list">
                    {activeClients.slice(0, 4).map((client) => (
                      <div key={client.id} className="list-item">
                        <div className="item-avatar">
                          <i className="fas fa-building"></i>
                        </div>
                        <div className="item-content">
                          <h4>{client.name}</h4>
                          <p>{client.type} • {client.location}</p>
                          <span className="item-meta">
                            {client.activeContracts} active contracts
                          </span>
                        </div>
                        <div className="item-value">
                          <span className="value">€{client.monthlyValue}</span>
                          <span className="period">/month</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <div className="empty-icon">
                      <i className="fas fa-users"></i>
                    </div>
                    <h4>No Active Clients</h4>
                    <p>Start building your client base by adding your first client.</p>
                    <button className="primary-btn" onClick={handleAddClient}>
                      <i className="fas fa-plus"></i>
                      Add Your First Client
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Deals */}
            <div className="content-card">
              <div className="card-header">
                <div className="header-title">
                  <i className="fas fa-handshake"></i>
                  <h3>Recent Deals</h3>
                </div>
                <button className="view-all-btn" onClick={handleViewAllDeals}>
                  View All
                </button>
              </div>
              <div className="card-body">
                {loading ? (
                  <div className="loading-state">
                    <i className="fas fa-spinner fa-spin"></i>
                    <p>Loading deals...</p>
                  </div>
                ) : recentDeals.length > 0 ? (
                  <div className="deal-list">
                    {recentDeals.slice(0, 4).map((deal) => (
                      <div key={deal.id} className="list-item">
                        <div className="item-avatar deal-avatar">
                          <i className="fas fa-handshake"></i>
                        </div>
                        <div className="item-content">
                          <h4>{deal.clientName}</h4>
                          <p>{deal.supplier} • {deal.energyType}</p>
                          <span className="item-meta">
                            {new Date(deal.closedDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="item-value">
                          <span className="value success">€{deal.commission}</span>
                          <span className={`status-badge ${deal.status.toLowerCase()}`}>
                            {deal.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <div className="empty-icon">
                      <i className="fas fa-handshake"></i>
                    </div>
                    <h4>No Recent Deals</h4>
                    <p>Your completed deals will appear here once you start closing contracts.</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Bottom Row - Actions & Metrics */}
          <div className="content-row">
            {/* Quick Actions */}
            <div className="content-card actions-card">
              <div className="card-header">
                <div className="header-title">
                  <i className="fas fa-bolt"></i>
                  <h3>Quick Actions</h3>
                </div>
              </div>
              <div className="card-body">
                <div className="actions-grid">
                  <button className="action-button primary" onClick={handleAddClient}>
                    <div className="action-icon">
                      <i className="fas fa-user-plus"></i>
                    </div>
                    <span>Add Client</span>
                  </button>
                  <button className="action-button" onClick={() => navigate('/compare-offers')}>
                    <div className="action-icon green">
                      <i className="fas fa-balance-scale"></i>
                    </div>
                    <span>Compare Offers</span>
                  </button>
                  <button className="action-button" onClick={handleViewAnalytics}>
                    <div className="action-icon blue">
                      <i className="fas fa-chart-line"></i>
                    </div>
                    <span>Analytics</span>
                  </button>
                  <button className="action-button" onClick={() => navigate('/profile')}>
                    <div className="action-icon gray">
                      <i className="fas fa-cog"></i>
                    </div>
                    <span>Settings</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Commission Overview */}
            <div className="content-card metrics-card">
              <div className="card-header">
                <div className="header-title">
                  <i className="fas fa-chart-bar"></i>
                  <h3>Commission Overview</h3>
                </div>
              </div>
              <div className="card-body">
                <div className="metrics-grid">
                  <div className="metric-item">
                    <div className="metric-label">This Month</div>
                    <div className="metric-value">€{stats.monthlyCommission.toLocaleString()}</div>
                    <div className="metric-change positive">+15%</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-label">Last Month</div>
                    <div className="metric-value">€2,450</div>
                    <div className="metric-change positive">+8%</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-label">YTD Total</div>
                    <div className="metric-value">€18,750</div>
                    <div className="metric-change positive">+22%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Pipeline Overview */}
          <div className="content-row single">
            <div className="content-card pipeline-card">
              <div className="card-header">
                <div className="header-title">
                  <i className="fas fa-funnel-dollar"></i>
                  <h3>Sales Pipeline</h3>
                </div>
              </div>
              <div className="card-body">
                <div className="pipeline-flow">
                  <div className="pipeline-stage">
                    <div className="stage-number">{stats?.pipelineData?.prospects || 12}</div>
                    <div className="stage-label">Prospects</div>
                  </div>
                  <div className="pipeline-arrow">
                    <i className="fas fa-arrow-right"></i>
                  </div>
                  <div className="pipeline-stage">
                    <div className="stage-number">{stats?.pipelineData?.qualified || 8}</div>
                    <div className="stage-label">Qualified</div>
                  </div>
                  <div className="pipeline-arrow">
                    <i className="fas fa-arrow-right"></i>
                  </div>
                  <div className="pipeline-stage">
                    <div className="stage-number">{stats?.pipelineData?.proposal || 5}</div>
                    <div className="stage-label">Proposal</div>
                  </div>
                  <div className="pipeline-arrow">
                    <i className="fas fa-arrow-right"></i>
                  </div>
                  <div className="pipeline-stage">
                    <div className="stage-number">{stats?.pipelineData?.closing || 3}</div>
                    <div className="stage-label">Closing</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrokerDashboard;
