import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/broker-dashboard.css';

const BrokerDashboard = () => {
  console.log('🎯 BROKER DASHBOARD COMPONENT - Component is rendering!');
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeClients, setActiveClients] = useState([]);
  const [recentDeals, setRecentDeals] = useState([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    activeDeals: 0,
    monthlyCommission: 0,
    conversionRate: 0
  });

  useEffect(() => {
    console.log('🎯 BROKER DASHBOARD COMPONENT - useEffect triggered');
    console.log('🎯 BROKER DASHBOARD COMPONENT - About to fetch dashboard data');
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🎯 BROKER DASHBOARD - fetchDashboardData started');
      setLoading(true);
      logger.info('Fetching broker dashboard data');

      // Fetch all data in parallel for better performance
      const [statsResponse, clientsResponse, dealsResponse] = await Promise.all([
        brokerService.getDashboardStats(),
        brokerService.getActiveClients(),
        brokerService.getRecentDeals()
      ]);

      console.log('🎯 BROKER DASHBOARD - All responses received:', {
        stats: statsResponse,
        clients: clientsResponse,
        deals: dealsResponse
      });

      // Set stats data
      if (statsResponse.success) {
        setStats(statsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Stats set:', statsResponse.data);
      }

      // Set clients data
      if (clientsResponse.success) {
        setActiveClients(clientsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Clients set:', clientsResponse.data.length, 'clients');
      }

      // Set deals data
      if (dealsResponse.success) {
        setRecentDeals(dealsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Deals set:', dealsResponse.data.length, 'deals');
      }

      logger.info('Broker dashboard data loaded successfully');
      console.log('🎯 BROKER DASHBOARD - Data loaded successfully');
    } catch (error) {
      logger.error('Error fetching broker dashboard data:', error);
      console.error('🎯 BROKER DASHBOARD - Error:', error);
      showErrorMessage('DATA_LOAD_FAILED');

      // Fallback to empty data
      setStats({
        totalClients: 0,
        activeDeals: 0,
        monthlyCommission: 0,
        conversionRate: 0
      });
      setActiveClients([]);
      setRecentDeals([]);
    } finally {
      setLoading(false);
      console.log('🎯 BROKER DASHBOARD - Loading set to false');
    }
  };

  const handleAddClient = () => {
    navigate('/add-client');
  };

  const handleViewAllClients = () => {
    navigate('/clients');
  };

  const handleViewAllDeals = () => {
    navigate('/deals');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="broker-dashboard-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading broker dashboard..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="broker-dashboard">
        {/* Dashboard Header */}
        <div className="broker-header">
          <div className="broker-header-content">
            <h1>Broker Dashboard</h1>
            <p>Manage your clients, track deals, and monitor your commission earnings</p>
          </div>
          <div className="broker-header-actions">
            <button className="btn-broker-action primary" onClick={handleAddClient}>
              <i className="fas fa-plus"></i>
              Add New Client
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="broker-stats-grid">
          <div className="stat-card clients">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : stats.totalClients}</h3>
              <p>Total Clients</p>
            </div>
          </div>

          <div className="stat-card deals">
            <div className="stat-icon">
              <i className="fas fa-handshake"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : stats.activeDeals}</h3>
              <p>Active Deals</p>
            </div>
          </div>

          <div className="stat-card commission">
            <div className="stat-icon">
              <i className="fas fa-euro-sign"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : `€${stats.monthlyCommission.toLocaleString()}`}</h3>
              <p>Monthly Commission</p>
            </div>
          </div>

          <div className="stat-card conversion">
            <div className="stat-icon">
              <i className="fas fa-percentage"></i>
            </div>
            <div className="stat-content">
              <h3>{loading ? '...' : `${stats.conversionRate}%`}</h3>
              <p>Conversion Rate</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="broker-content-grid">
          {/* Active Clients */}
          <div className="broker-section">
            <div className="section-header">
              <h3>
                <i className="fas fa-users"></i>
                Active Clients
              </h3>
              <button className="btn-broker-link" onClick={handleViewAllClients}>
                View All
              </button>
            </div>
            <div className="section-content">
              {loading ? (
                <div className="loading-state">
                  <i className="fas fa-spinner fa-spin"></i>
                  <p>Loading clients...</p>
                </div>
              ) : activeClients.length > 0 ? (
                <div className="clients-list">
                  {activeClients.slice(0, 5).map((client) => (
                    <div key={client.id} className="client-item">
                      <div className="client-info">
                        <h4>{client.name}</h4>
                        <p>{client.type} • {client.location}</p>
                        <span className="client-status">
                          {client.activeContracts} active contracts
                        </span>
                      </div>
                      <div className="client-value">
                        <span className="value">€{client.monthlyValue}</span>
                        <span className="period">/month</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-users"></i>
                  <h4>No Active Clients</h4>
                  <p>Start building your client base by adding your first client.</p>
                  <button className="btn-broker-action primary" onClick={handleAddClient}>
                    <i className="fas fa-plus"></i>
                    Add Your First Client
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Deals */}
          <div className="broker-section">
            <div className="section-header">
              <h3>
                <i className="fas fa-handshake"></i>
                Recent Deals
              </h3>
              <button className="btn-broker-link" onClick={handleViewAllDeals}>
                View All
              </button>
            </div>
            <div className="section-content">
              {loading ? (
                <div className="loading-state">
                  <i className="fas fa-spinner fa-spin"></i>
                  <p>Loading deals...</p>
                </div>
              ) : recentDeals.length > 0 ? (
                <div className="deals-list">
                  {recentDeals.slice(0, 5).map((deal) => (
                    <div key={deal.id} className="deal-item">
                      <div className="deal-info">
                        <h4>{deal.clientName}</h4>
                        <p>{deal.supplier} • {deal.energyType}</p>
                        <span className="deal-date">
                          {new Date(deal.closedDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="deal-commission">
                        <span className="commission">€{deal.commission}</span>
                        <span className={`status-badge ${deal.status.toLowerCase()}`}>
                          {deal.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <i className="fas fa-handshake"></i>
                  <h4>No Recent Deals</h4>
                  <p>Your completed deals will appear here once you start closing contracts.</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="broker-section quick-actions-section">
            <div className="section-header">
              <h3>
                <i className="fas fa-bolt"></i>
                Quick Actions
              </h3>
            </div>
            <div className="section-content">
              <div className="quick-actions-grid" style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(4, 1fr)',
                gap: '24px',
                marginTop: '16px'
              }}>
                <button className="action-btn primary-action" onClick={handleAddClient} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '12px',
                  padding: '24px 16px',
                  border: '1px solid #2563eb',
                  borderRadius: '12px',
                  background: '#2563eb',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: '600',
                  minHeight: '120px',
                  textAlign: 'center',
                  boxShadow: '0 2px 8px rgba(37, 99, 235, 0.2)',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}>
                  <i className="fas fa-user-plus" style={{ fontSize: '28px' }}></i>
                  <span>Add New Client</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/compare-offers')} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '12px',
                  padding: '24px 16px',
                  border: '1px solid #e5e7eb',
                  borderRadius: '12px',
                  background: '#ffffff',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '600',
                  minHeight: '120px',
                  textAlign: 'center',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}>
                  <i className="fas fa-balance-scale" style={{ fontSize: '28px', color: '#059669' }}></i>
                  <span>Compare Offers</span>
                </button>
                <button className="action-btn" onClick={handleViewAnalytics} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '12px',
                  padding: '24px 16px',
                  border: '1px solid #e5e7eb',
                  borderRadius: '12px',
                  background: '#ffffff',
                  color: '#374151',
                  fontSize: '14px',
                  fontWeight: '600',
                  minHeight: '120px',
                  textAlign: 'center',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}>
                  <i className="fas fa-chart-line" style={{ fontSize: '40px' }}></i>
                  <span>View Analytics</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/profile')} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '16px',
                  padding: '40px 24px',
                  border: '3px solid #e0e0e0',
                  borderRadius: '16px',
                  background: '#ffffff',
                  color: '#333333',
                  fontSize: '16px',
                  fontWeight: '700',
                  minHeight: '160px',
                  textAlign: 'center',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}>
                  <i className="fas fa-cog" style={{ fontSize: '40px' }}></i>
                  <span>Settings</span>
                </button>
              </div>
            </div>
          </div>

          {/* Commission Overview */}
          <div className="broker-section">
            <div className="section-header">
              <h3>
                <i className="fas fa-chart-bar"></i>
                Commission Overview
              </h3>
            </div>
            <div className="section-content">
              <div className="commission-metrics">
                <div className="metric">
                  <span className="metric-label">This Month</span>
                  <span className="metric-value">€{stats.monthlyCommission}</span>
                </div>
                <div className="metric">
                  <span className="metric-label">Last Month</span>
                  <span className="metric-value">€2,450</span>
                </div>
                <div className="metric">
                  <span className="metric-label">YTD Total</span>
                  <span className="metric-value">€18,750</span>
                </div>
              </div>
            </div>
          </div>

          {/* Pipeline Overview */}
          <div className="broker-section">
            <div className="section-header">
              <h3>
                <i className="fas fa-funnel-dollar"></i>
                Sales Pipeline
              </h3>
            </div>
            <div className="section-content">
              <div className="pipeline-stages">
                <div className="stage">
                  <span className="stage-label">Prospects</span>
                  <span className="stage-count">{stats?.pipelineData?.prospects || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Qualified</span>
                  <span className="stage-count">{stats?.pipelineData?.qualified || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Proposal</span>
                  <span className="stage-count">{stats?.pipelineData?.proposal || 0}</span>
                </div>
                <div className="stage">
                  <span className="stage-label">Closing</span>
                  <span className="stage-count">{stats?.pipelineData?.closing || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrokerDashboard;
