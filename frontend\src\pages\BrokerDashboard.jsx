import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/broker-dashboard.css';

const BrokerDashboard = () => {
  console.log('🎯 BROKER DASHBOARD COMPONENT - Component is rendering!');
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeClients, setActiveClients] = useState([]);
  const [recentDeals, setRecentDeals] = useState([]);
  const [stats, setStats] = useState({
    totalClients: 0,
    activeDeals: 0,
    monthlyCommission: 0,
    conversionRate: 0
  });

  useEffect(() => {
    console.log('🎯 BROKER DASHBOARD COMPONENT - useEffect triggered');
    console.log('🎯 BROKER DASHBOARD COMPONENT - About to fetch dashboard data');
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🎯 BROKER DASHBOARD - fetchDashboardData started');
      setLoading(true);
      logger.info('Fetching broker dashboard data');

      // Fetch all data in parallel for better performance
      const [statsResponse, clientsResponse, dealsResponse] = await Promise.all([
        brokerService.getDashboardStats(),
        brokerService.getActiveClients(),
        brokerService.getRecentDeals()
      ]);

      console.log('🎯 BROKER DASHBOARD - All responses received:', {
        stats: statsResponse,
        clients: clientsResponse,
        deals: dealsResponse
      });

      // Set stats data
      if (statsResponse.success) {
        setStats(statsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Stats set:', statsResponse.data);
      }

      // Set clients data
      if (clientsResponse.success) {
        setActiveClients(clientsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Clients set:', clientsResponse.data.length, 'clients');
      }

      // Set deals data
      if (dealsResponse.success) {
        setRecentDeals(dealsResponse.data);
        console.log('🎯 BROKER DASHBOARD - Deals set:', dealsResponse.data.length, 'deals');
      }

      logger.info('Broker dashboard data loaded successfully');
      console.log('🎯 BROKER DASHBOARD - Data loaded successfully');
    } catch (error) {
      logger.error('Error fetching broker dashboard data:', error);
      console.error('🎯 BROKER DASHBOARD - Error:', error);
      showErrorMessage('DATA_LOAD_FAILED');

      // Fallback to empty data
      setStats({
        totalClients: 0,
        activeDeals: 0,
        monthlyCommission: 0,
        conversionRate: 0
      });
      setActiveClients([]);
      setRecentDeals([]);
    } finally {
      setLoading(false);
      console.log('🎯 BROKER DASHBOARD - Loading set to false');
    }
  };

  const handleAddClient = () => {
    navigate('/add-client');
  };

  const handleViewAllClients = () => {
    navigate('/clients');
  };

  const handleViewAllDeals = () => {
    navigate('/deals');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="broker-dashboard-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading broker dashboard..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="broker-dashboard">
        {/* Welcome Header */}
        <div className="dashboard-header">
          <div className="header-content">
            <h1>Welcome back!</h1>
            <p>Here's what's happening with your business today</p>
          </div>
          <div className="header-actions">
            <button className="btn-primary" onClick={handleAddClient}>
              <i className="fas fa-plus"></i>
              Add Client
            </button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon clients">
                <i className="fas fa-users"></i>
              </div>
              <div className="stat-trend positive">
                <i className="fas fa-arrow-up"></i>
                +12%
              </div>
            </div>
            <div className="stat-content">
              <h3>{stats.totalClients}</h3>
              <p>Total Clients</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon deals">
                <i className="fas fa-handshake"></i>
              </div>
              <div className="stat-trend positive">
                <i className="fas fa-arrow-up"></i>
                +8%
              </div>
            </div>
            <div className="stat-content">
              <h3>{stats.activeDeals}</h3>
              <p>Active Deals</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon revenue">
                <i className="fas fa-euro-sign"></i>
              </div>
              <div className="stat-trend positive">
                <i className="fas fa-arrow-up"></i>
                +15%
              </div>
            </div>
            <div className="stat-content">
              <h3>€{stats.monthlyCommission.toLocaleString()}</h3>
              <p>Monthly Revenue</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-header">
              <div className="stat-icon conversion">
                <i className="fas fa-chart-line"></i>
              </div>
              <div className="stat-trend positive">
                <i className="fas fa-arrow-up"></i>
                +5%
              </div>
            </div>
            <div className="stat-content">
              <h3>{stats.conversionRate}%</h3>
              <p>Conversion Rate</p>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="content-grid">
          {/* Active Clients */}
          <div className="content-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-users"></i>
                Active Clients
              </h3>
              <button className="btn-link" onClick={handleViewAllClients}>
                View All
              </button>
            </div>
            <div className="card-content">
              {activeClients.length > 0 ? (
                <div className="client-list">
                  {activeClients.slice(0, 5).map((client) => (
                    <div key={client.id} className="client-item">
                      <div className="client-avatar">
                        <i className="fas fa-building"></i>
                      </div>
                      <div className="client-info">
                        <h4>{client.name}</h4>
                        <p>{client.type} • {client.location}</p>
                        <span className="client-meta">
                          {client.activeContracts} active contracts
                        </span>
                      </div>
                      <div className="client-value">
                        <span className="value">€{client.monthlyValue}</span>
                        <span className="period">/month</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <div className="empty-icon">
                    <i className="fas fa-users"></i>
                  </div>
                  <h4>No Active Clients</h4>
                  <p>Start building your client base by adding your first client.</p>
                  <button className="btn-primary" onClick={handleAddClient}>
                    <i className="fas fa-plus"></i>
                    Add Your First Client
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Recent Deals */}
          <div className="content-card">
            <div className="card-header">
              <h3>
                <i className="fas fa-handshake"></i>
                Recent Deals
              </h3>
              <button className="btn-link" onClick={handleViewAllDeals}>
                View All
              </button>
            </div>
            <div className="card-content">
              {recentDeals.length > 0 ? (
                <div className="deal-list">
                  {recentDeals.slice(0, 5).map((deal) => (
                    <div key={deal.id} className="deal-item">
                      <div className="deal-icon">
                        <i className="fas fa-handshake"></i>
                      </div>
                      <div className="deal-info">
                        <h4>{deal.clientName}</h4>
                        <p>{deal.supplier} • {deal.energyType}</p>
                        <span className="deal-date">
                          {new Date(deal.closedDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="deal-value">
                        <span className="commission">€{deal.commission}</span>
                        <span className={`status-badge ${deal.status.toLowerCase()}`}>
                          {deal.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <div className="empty-icon">
                    <i className="fas fa-handshake"></i>
                  </div>
                  <h4>No Recent Deals</h4>
                  <p>Your completed deals will appear here once you start closing contracts.</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="content-card quick-actions">
            <div className="card-header">
              <h3>
                <i className="fas fa-bolt"></i>
                Quick Actions
              </h3>
            </div>
            <div className="card-content">
              <div className="actions-grid">
                <button className="action-btn primary" onClick={handleAddClient}>
                  <div className="action-icon">
                    <i className="fas fa-user-plus"></i>
                  </div>
                  <span>Add Client</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/compare-offers')}>
                  <div className="action-icon">
                    <i className="fas fa-balance-scale"></i>
                  </div>
                  <span>Compare Offers</span>
                </button>
                <button className="action-btn" onClick={handleViewAnalytics}>
                  <div className="action-icon">
                    <i className="fas fa-chart-line"></i>
                  </div>
                  <span>Analytics</span>
                </button>
                <button className="action-btn" onClick={() => navigate('/profile')}>
                  <div className="action-icon">
                    <i className="fas fa-cog"></i>
                  </div>
                  <span>Settings</span>
                </button>
              </div>
            </div>
          </div>

          {/* Performance Overview */}
          <div className="content-card performance">
            <div className="card-header">
              <h3>
                <i className="fas fa-chart-line"></i>
                Performance Overview
              </h3>
            </div>
            <div className="card-content">
              <div className="performance-metrics">
                <div className="metric-item">
                  <div className="metric-label">This Month</div>
                  <div className="metric-value">€{stats.monthlyCommission.toLocaleString()}</div>
                  <div className="metric-change positive">+15%</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">Last Month</div>
                  <div className="metric-value">€2,450</div>
                  <div className="metric-change positive">+8%</div>
                </div>
                <div className="metric-item">
                  <div className="metric-label">YTD Total</div>
                  <div className="metric-value">€18,750</div>
                  <div className="metric-change positive">+22%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Pipeline */}
        <div className="pipeline-section">
          <div className="content-card pipeline">
            <div className="card-header">
              <h3>
                <i className="fas fa-funnel-dollar"></i>
                Sales Pipeline
              </h3>
            </div>
            <div className="card-content">
              <div className="pipeline-stages">
                <div className="pipeline-stage">
                  <div className="stage-number">{stats?.pipelineData?.prospects || 12}</div>
                  <div className="stage-label">Prospects</div>
                </div>
                <div className="pipeline-arrow">
                  <i className="fas fa-arrow-right"></i>
                </div>
                <div className="pipeline-stage">
                  <div className="stage-number">{stats?.pipelineData?.qualified || 8}</div>
                  <div className="stage-label">Qualified</div>
                </div>
                <div className="pipeline-arrow">
                  <i className="fas fa-arrow-right"></i>
                </div>
                <div className="pipeline-stage">
                  <div className="stage-number">{stats?.pipelineData?.proposal || 5}</div>
                  <div className="stage-label">Proposal</div>
                </div>
                <div className="pipeline-arrow">
                  <i className="fas fa-arrow-right"></i>
                </div>
                <div className="pipeline-stage">
                  <div className="stage-number">{stats?.pipelineData?.closing || 3}</div>
                  <div className="stage-label">Closing</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrokerDashboard;
