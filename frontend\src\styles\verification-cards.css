/* Verification Cards Styles */

.verification-card {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  position: relative;
  box-sizing: border-box;
  height: 100%;
}

.verification-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.verification-card .card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 16px;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
  box-sizing: border-box;
}

.verification-card .card-body {
  padding: 20px 24px;
  box-sizing: border-box;
}

/* Status Card Specific Styles */
.status-card .card-header {
  position: relative;
}

.status-card .status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.status-card .status-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, transparent 100%);
  border-radius: 50%;
}

.status-card .status-icon.blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
.status-card .status-icon.orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}
.status-card .status-icon.yellow {
  background: linear-gradient(135deg, #eab308 0%, #ca8a04 100%);
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.3);
}
.status-card .status-icon.red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}
.status-card .status-icon.gray {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.status-card .status-content {
  flex: 1;
  min-width: 0;
}

.status-card .status-content h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 700;
  color: #111827;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.status-card .status-message {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
}

.status-card .refresh-btn {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-size: 18px;
  margin-left: auto;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-card .refresh-btn:hover {
  background: #f1f5f9;
  color: #475569;
  transform: rotate(180deg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-card .timeline-info {
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.status-card .timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 15px;
  color: #64748b;
  font-weight: 500;
}

.status-card .timeline-item:last-child {
  margin-bottom: 0;
}

.status-card .timeline-item i {
  width: 16px;
  color: #94a3b8;
  font-size: 14px;
  text-align: center;
}

.status-card .status-description {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border-left: 4px solid #4f46e5;
  position: relative;
}

.status-card .status-description::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, transparent 100%);
  border-radius: 12px;
  pointer-events: none;
}

.status-card .status-description p {
  margin: 0;
  color: #475569;
  line-height: 1.6;
  font-size: 15px;
  position: relative;
  z-index: 1;
}

.status-card .changes-requested {
  margin-top: 16px;
  padding: 16px;
  background-color: #fef3c7;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.status-card .changes-requested h4 {
  margin: 0 0 12px 0;
  color: #92400e;
  font-size: 14px;
  font-weight: 600;
}

.status-card .changes-requested ul {
  margin: 0;
  padding-left: 16px;
}

.status-card .changes-requested li {
  margin-bottom: 8px;
  color: #92400e;
  font-size: 14px;
}

.status-card .admin-notes {
  margin-top: 16px;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #0ea5e9;
}

.status-card .admin-notes h4 {
  margin: 0 0 8px 0;
  color: #0c4a6e;
  font-size: 14px;
  font-weight: 600;
}

.status-card .admin-notes p {
  margin: 0;
  color: #0c4a6e;
  font-size: 14px;
  line-height: 1.5;
}

/* Next Steps Card Styles */
.next-steps-card .process-timeline {
  margin-bottom: 24px;
}

.next-steps-card .timeline-step-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.next-steps-card .timeline-step {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.next-steps-card .step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  color: #9ca3af;
  font-size: 16px;
  transition: all 0.3s ease;
}

.next-steps-card .timeline-step.completed .step-icon {
  background-color: #10b981;
  color: white;
}

.next-steps-card .timeline-step.current .step-icon {
  background-color: #3b82f6;
  color: white;
  animation: pulse 2s infinite;
}

.next-steps-card .timeline-step.action-required .step-icon {
  background-color: #f59e0b;
  color: white;
  animation: bounce 1s infinite;
}

.next-steps-card .step-content {
  flex: 1;
}

.next-steps-card .step-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.next-steps-card .step-status {
  display: block;
  font-size: 12px;
  color: #6b7280;
}

.next-steps-card .step-status.action {
  color: #f59e0b;
  font-weight: 500;
}

.next-steps-card .step-connector {
  width: 2px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 19px;
  transition: background-color 0.3s ease;
}

.next-steps-card .step-connector.completed {
  background-color: #10b981;
}

.next-steps-card .next-step-info {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 16px;
}

.next-steps-card .next-step-info.urgent {
  background-color: #fef3c7;
  border-left: 4px solid #f59e0b;
}

.next-steps-card .next-step-info h4 {
  margin: 0 0 8px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.next-steps-card .next-step-info p {
  margin: 0 0 16px 0;
  color: #6b7280;
  line-height: 1.5;
}

.next-steps-card .next-step-action {
  margin-top: 12px;
}

.next-steps-card .estimated-timeline {
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #0ea5e9;
}

/* Profile Preview Card Styles */
.profile-preview-card .card-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.profile-preview-card .profile-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.profile-preview-card .profile-title h3 {
  margin: 0 0 8px 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
}

.profile-preview-card .completeness-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-preview-card .completeness-bar {
  width: 120px;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.profile-preview-card .completeness-fill {
  height: 100%;
  background-color: #10b981;
  transition: width 0.3s ease;
}

.profile-preview-card .completeness-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.profile-preview-card .profile-fields {
  margin-bottom: 20px;
}

.profile-preview-card .profile-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.profile-preview-card .profile-field:last-child {
  border-bottom: none;
}

.profile-preview-card .field-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.profile-preview-card .field-value {
  color: #6b7280;
  font-size: 14px;
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

.profile-preview-card .profile-status {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
}

.profile-preview-card .status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.profile-preview-card .status-indicator.success {
  color: #059669;
}

.profile-preview-card .status-indicator.warning {
  color: #d97706;
}

.profile-preview-card .status-indicator.success {
  background-color: #ecfdf5;
}

.profile-preview-card .status-indicator.warning {
  background-color: #fef3c7;
}

.profile-preview-card .submission-date,
.profile-preview-card .incomplete-message {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.profile-preview-card .profile-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-preview-card .view-only-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
  color: #6b7280;
  font-size: 12px;
}

.profile-preview-card .changes-notice {
  margin-top: 16px;
  padding: 12px;
  background-color: #fef3c7;
  border-radius: 8px;
  border-left: 4px solid #f59e0b;
}

.profile-preview-card .notice-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #92400e;
  font-weight: 500;
  font-size: 14px;
}

.profile-preview-card .changes-notice p {
  margin: 0;
  color: #92400e;
  font-size: 13px;
  line-height: 1.4;
}

/* Support Contact Card Styles */
.support-contact-card .card-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.support-contact-card .support-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.support-contact-card .support-title h3 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
}

.support-contact-card .support-title p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.support-contact-card .support-options {
  margin-bottom: 24px;
}

.support-contact-card .support-option {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.support-contact-card .support-option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.support-contact-card .option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.support-contact-card .option-content {
  flex: 1;
}

.support-contact-card .option-content h4 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 14px;
  font-weight: 600;
}

.support-contact-card .option-content p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

.support-contact-card .quick-tips h4 {
  margin: 0 0 12px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.support-contact-card .tips-list {
  margin-bottom: 20px;
}

.support-contact-card .tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #6b7280;
}

.support-contact-card .tip-item i {
  width: 16px;
  color: #10b981;
}

.support-contact-card .emergency-contact {
  padding: 16px;
  background-color: #fef2f2;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.support-contact-card .emergency-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #dc2626;
  font-weight: 500;
  font-size: 14px;
}

.support-contact-card .emergency-contact p {
  margin: 0;
  color: #dc2626;
  font-size: 13px;
  line-height: 1.4;
}

/* Platform Resources Card Styles */
.platform-resources-card .card-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.platform-resources-card .resources-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.platform-resources-card .resources-title h3 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
}

.platform-resources-card .resources-title p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.platform-resources-card .resource-section {
  margin-bottom: 24px;
}

.platform-resources-card .resource-section h4 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.platform-resources-card .resources-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.platform-resources-card .resource-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.platform-resources-card .resource-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.platform-resources-card .resource-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.platform-resources-card .resource-content {
  flex: 1;
}

.platform-resources-card .resource-content h5 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 14px;
  font-weight: 600;
}

.platform-resources-card .resource-content p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

.platform-resources-card .resource-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #3b82f6;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.platform-resources-card .resource-link:hover {
  color: #2563eb;
}

.platform-resources-card .preparation-tips h4 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.platform-resources-card .tips-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.platform-resources-card .tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #f9fafb;
  border-radius: 6px;
  font-size: 12px;
  color: #374151;
}

.platform-resources-card .tip-item i {
  color: #8b5cf6;
  width: 16px;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-4px); }
  60% { transform: translateY(-2px); }
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .verification-card .card-header,
  .verification-card .card-body {
    padding: 16px;
  }
  
  .platform-resources-card .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .next-steps-card .timeline-step-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .next-steps-card .step-connector {
    width: 24px;
    height: 2px;
    margin: 8px 0;
  }
}
