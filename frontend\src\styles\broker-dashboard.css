/* Broker Dashboard - Professional Layout */

.broker-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: #ffffff;
  min-height: calc(100vh - 60px);
}

.broker-dashboard-container {
  width: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Header Section - Black & White Theme */
.broker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 28px 32px;
  background: #ffffff;
  border-radius: 16px;
  color: #1f2937;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid #000000;
}

.broker-header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.025em;
  color: #1e293b;
}

.broker-header-content p {
  margin: 0;
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

.broker-header-actions {
  display: flex;
  gap: 12px;
}

.btn-broker-action {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.btn-broker-action.primary {
  background: #000000;
  color: #ffffff;
  border: 1px solid #000000;
}

.btn-broker-action.primary:hover {
  background: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Stats Grid - Consistent Heights */
.broker-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 100px;
  height: 100px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #000000;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
  background: #000000;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.stat-content p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* Dashboard Content - Consistent Heights */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: stretch;
}

.content-row.single {
  grid-template-columns: 1fr;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.content-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  padding: 18px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  flex-shrink: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title i {
  color: #000000;
  font-size: 16px;
}

.header-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.view-all-btn {
  padding: 8px 16px;
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.card-body {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-card {
    min-height: 350px;
  }
}

@media (max-width: 768px) {
  .broker-dashboard {
    padding: 16px;
  }

  .broker-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 20px;
  }

  .broker-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-card {
    min-height: 300px;
  }

  .broker-header-content h1 {
    font-size: 24px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .pipeline-flow {
    flex-direction: column;
    gap: 16px;
  }

  .pipeline-arrow {
    transform: rotate(90deg);
  }

  .stat-card {
    height: auto;
    min-height: 80px;
  }
}

/* Additional styles for missing components */
.loading-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.list-item:last-child {
  border-bottom: none;
}

.item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.item-content {
  flex: 1;
}

.item-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.item-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.item-value {
  text-align: right;
}

/* Specific Card Layouts */
.actions-card .card-body {
  justify-content: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #1f2937;
  font-size: 14px;
  font-weight: 500;
}

.action-button:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-button.primary {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.action-button.primary:hover {
  background: #1f2937;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  background: #e2e8f0;
  color: #1f2937;
}

.action-icon.green {
  background: #dcfce7;
  color: #166534;
}

.action-icon.blue {
  background: #dbeafe;
  color: #1e40af;
}

.action-icon.gray {
  background: #f1f5f9;
  color: #64748b;
}

.action-button.primary .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Metrics Card */
.metrics-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  justify-content: space-around;
}

.metric-item {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.metric-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: #059669;
}

.metric-change.negative {
  color: #dc2626;
}

/* Pipeline Card */
.pipeline-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 20px 0;
}

.pipeline-stage {
  text-align: center;
  flex: 1;
}

.stage-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stage-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.pipeline-arrow {
  color: #cbd5e1;
  font-size: 16px;
}

/* List Items */
.client-list,
.deal-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  height: 100%;
  overflow-y: auto;
}

.list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.list-item:last-child {
  border-bottom: none;
}

.item-content h4 {
  font-size: 14px;
  margin-bottom: 2px;
}

.item-content p {
  font-size: 12px;
  margin-bottom: 2px;
}

.item-meta {
  font-size: 11px;
  color: #9ca3af;
}

.value {
  font-weight: 600;
  color: #1f2937;
}

.value.success {
  color: #059669;
}

.period {
  font-size: 11px;
  color: #6b7280;
}

/* Status badges for deals */
.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: 2px;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.completed {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}
