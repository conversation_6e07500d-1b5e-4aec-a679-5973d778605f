/* Broker Dashboard - Modern Professional Theme */
.broker-dashboard {
  padding: 24px;
  width: 100%;
  min-height: calc(100vh - 80px);
  background: #f8fafc;
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
}

.broker-dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f8fafc;
}

/* Header */
.broker-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32px 40px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.broker-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.broker-header-content h1 {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.broker-header-content p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  line-height: 1.6;
}

.broker-header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-shrink: 0;
}

.btn-broker-action {
  padding: 14px 28px;
  border: 2px solid #e2e8f0;
  background: #ffffff;
  color: #475569;
  border-radius: 12px;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-broker-action:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-broker-action.primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #ffffff;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

.btn-broker-action.primary:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

/* Stats Grid */
.broker-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #cbd5e1;
}

.stat-card .stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

.stat-card .stat-icon i {
  font-size: 24px;
}

.stat-card .stat-content h3 {
  font-size: 32px;
  font-weight: 800;
  margin: 0 0 6px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.stat-card .stat-content p {
  font-size: 15px;
  color: #64748b;
  margin: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Content Grid */
.broker-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
  gap: 32px;
  margin-top: 8px;
}

/* Sections */
.broker-section {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.broker-section:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.section-header {
  padding: 28px 32px 20px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
  margin-bottom: 0;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
}

.section-header h3 {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: -0.025em;
}

.section-header h3 i {
  color: #64748b;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.btn-broker-link {
  color: #475569;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  padding: 10px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #ffffff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-broker-link:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.section-content {
  padding: 32px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #64748b;
}

.loading-state i {
  font-size: 28px;
  margin-bottom: 16px;
  color: #4f46e5;
}

.loading-state p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 32px;
  color: #64748b;
}

.empty-state i {
  font-size: 56px;
  margin-bottom: 20px;
  color: #cbd5e1;
}

.empty-state h4 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.empty-state p {
  font-size: 16px;
  margin: 0 0 28px 0;
  color: #64748b;
  line-height: 1.6;
}

/* Clients List */
.clients-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #f1f5f9;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: #ffffff;
  cursor: pointer;
}

.client-item:hover {
  border-color: #e2e8f0;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.client-info h4 {
  font-size: 17px;
  font-weight: 700;
  margin: 0 0 6px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.client-info p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 6px 0;
  font-weight: 500;
}

.client-status {
  font-size: 13px;
  color: #059669;
  font-weight: 600;
  background: #d1fae5;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.client-value {
  text-align: right;
  flex-shrink: 0;
}

.client-value .value {
  font-size: 20px;
  font-weight: 800;
  color: #1e293b;
  display: block;
  letter-spacing: -0.025em;
}

.client-value .period {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

/* Deals List */
.deals-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.deal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #f1f5f9;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: #ffffff;
  cursor: pointer;
}

.deal-item:hover {
  border-color: #e2e8f0;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.deal-info h4 {
  font-size: 17px;
  font-weight: 700;
  margin: 0 0 6px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.deal-info p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 6px 0;
  font-weight: 500;
}

.deal-date {
  font-size: 13px;
  color: #94a3b8;
  font-weight: 500;
}

.deal-commission {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

.deal-commission .commission {
  font-size: 20px;
  font-weight: 800;
  color: #059669;
  letter-spacing: -0.025em;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.completed {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

/* Quick Actions */
.quick-actions-section {
  grid-column: 1 / -1;
  margin-top: 16px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.action-btn {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 16px !important;
  padding: 32px 24px !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 16px !important;
  background: #ffffff !important;
  color: #475569 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  min-height: 140px !important;
  text-align: center !important;
}

.action-btn:hover {
  border-color: #cbd5e1 !important;
  transform: translateY(-4px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  background: #f8fafc !important;
}

.action-btn.primary-action {
  border-color: transparent !important;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  color: #ffffff !important;
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3) !important;
}

.action-btn.primary-action:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%) !important;
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.4) !important;
}

.action-btn i {
  font-size: 36px !important;
  margin-bottom: 0 !important;
  color: inherit !important;
}

.action-btn span {
  font-size: 15px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  color: inherit !important;
}

/* Commission Metrics */
.commission-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.metric {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  font-weight: 500;
}

.metric-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #000000;
}

/* Pipeline Stages */
.pipeline-stages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stage {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.stage-label {
  display: block;
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  font-weight: 500;
}

.stage-count {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #000000;
}

/* Responsive */
@media (max-width: 768px) {
  .broker-dashboard {
    padding: 16px;
  }

  .broker-header {
    flex-direction: column;
    gap: 24px;
    text-align: left;
    padding: 28px 24px;
  }

  .broker-header-content h1 {
    font-size: 28px;
  }

  .broker-header-content p {
    font-size: 16px;
  }

  .broker-header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .broker-content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .action-btn {
    min-height: 120px !important;
    padding: 24px 16px !important;
  }

  .action-btn i {
    font-size: 28px !important;
  }

  .section-header {
    padding: 24px 24px 16px 24px;
  }

  .section-content {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .broker-dashboard {
    padding: 12px;
  }

  .broker-header {
    padding: 24px 20px;
  }

  .broker-header-content h1 {
    font-size: 24px;
  }

  .broker-header-content p {
    font-size: 15px;
  }

  .broker-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 20px;
  }

  .section-header {
    padding: 20px 20px 16px 20px;
  }

  .section-header h3 {
    font-size: 18px;
  }

  .section-content {
    padding: 20px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-btn {
    min-height: 100px !important;
    padding: 20px 16px !important;
    gap: 12px !important;
  }

  .action-btn i {
    font-size: 24px !important;
  }

  .action-btn span {
    font-size: 13px !important;
  }
}

.broker-dashboard-container .header-actions .btn-primary:hover {
  background: #fff;
  color: #000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Broker Stats Grid - Identical to dashboard-stats.css but with broker- prefix */
.broker-stats-container {
  width: 100%;
  margin-bottom: 25px;
  padding: 0;
  max-width: 100%;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.broker-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px;
  padding: 0;
  width: 100%;
  align-items: stretch;
}

@media (max-width: 1200px) {
  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* New elegant card design */
.broker-stat-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.25s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #000;
  height: 140px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #000;
  overflow: hidden;
  width: 100%;
}

.broker-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: #f9f9f9;
}

/* Refined icon style */
.broker-stat-card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  background: transparent;
}

.broker-stat-card-icon svg {
  width: 30px;
  height: 30px;
}

.broker-stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.broker-stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 0 0 5px 0;
  letter-spacing: 0.3px;
}

.broker-stat-card-value {
  font-size: 42px;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.broker-stat-card-description {
  font-size: 13px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
  max-width: 90%;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background: #fff;
  border: 1px solid #000;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #111827;
}

.card-header h3 i {
  color: #000;
}

.btn-link {
  background: none;
  border: none;
  color: #000;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.btn-link:hover {
  color: #333;
}

.card-content {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}



/* Clients List */
.clients-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.client-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.client-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.client-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.client-status {
  color: #27ae60;
  font-size: 0.85rem;
  font-weight: 500;
}

.client-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.client-value .value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.client-value .period {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Deals List */
.deals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.deal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.deal-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.deal-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.deal-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.deal-date {
  color: #000;
  font-size: 0.85rem;
  font-weight: 500;
}

.deal-commission {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.deal-commission .commission {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}









/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .broker-dashboard-container {
    padding: 1rem;
    background: transparent;
    min-height: calc(100vh - 60px);
  }

  .broker-dashboard-container .dashboard-header {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .broker-dashboard-container .header-content {
    text-align: center;
    width: 100%;
  }

  .broker-dashboard-container .header-content h1 {
    font-size: 1.6rem;
    justify-content: center;
  }

  .broker-dashboard-container .header-content h1 i {
    font-size: 1.4rem;
  }

  .broker-dashboard-container .header-actions {
    width: 100%;
    justify-content: center;
  }

  .broker-dashboard-container .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  /* Broker Stats Mobile Styles */
  .broker-stats-container {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
    width: calc(100% - 20px) !important;
    box-sizing: border-box !important;
  }

  .broker-stats-container .broker-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 15px !important;
    height: auto !important;
    min-height: 80px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: 1fr auto !important;
    gap: 15px !important;
    align-items: center !important;
    position: relative !important;
  }

  .broker-stats-container .broker-stat-card-content {
    grid-column: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 8px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 12px !important;
    line-height: 1.3 !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-value {
    grid-column: 2 !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin: 0 !important;
    line-height: 1 !important;
    position: static !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    height: auto !important;
    bottom: auto !important;
    right: auto !important;
  }

  .header-content h1 {
    font-size: 1.6rem;
  }

  .header-content h1 i {
    font-size: 1.4rem;
  }

  .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 1.3rem;
  }

  .stat-content h3 {
    font-size: 1.8rem;
  }

  .dashboard-content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .client-item,
  .deal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .client-value,
  .deal-commission {
    width: 100%;
    align-items: flex-start;
  }



  .pipeline-stages {
    flex-direction: column;
    gap: 1rem;
  }

  .stage {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
  }

  .stage-label {
    font-size: 0.9rem;
    margin-bottom: 0;
  }

  .stage-count {
    font-size: 1.3rem;
  }
}

/* Small Mobile Screens */
@media (max-width: 480px) {
  .broker-dashboard-container {
    padding: 0.5rem;
  }

  .broker-dashboard-container .dashboard-header {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .broker-stats-container {
    padding: 0 5px !important;
    width: calc(100% - 10px) !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 12px !important;
    min-height: 70px !important;
    gap: 10px !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 24px !important;
    height: 24px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 13px !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 11px !important;
  }

  .broker-stats-container .broker-stat-card-value {
    font-size: 32px !important;
    min-width: 50px !important;
  }

  .header-content h1 {
    font-size: 1.3rem;
  }

  .stats-grid {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-content h3 {
    font-size: 1.3rem;
  }

  .dashboard-content-grid {
    gap: 0.75rem;
  }

  .card-header {
    padding: 0.75rem;
  }

  .card-content {
    padding: 0.75rem;
  }

  .stage {
    padding: 0.5rem;
  }

  .stage-label {
    font-size: 0.8rem;
  }

  .stage-count {
    font-size: 1.1rem;
  }
}

/* Dashboard Summary Styles */
.dashboard-summary {
  margin: 2rem 0;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.summary-content h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.summary-stat {
  text-align: center;
}

.summary-stat .stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.summary-stat .stat-label {
  display: block;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Loading State Styles */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6c757d;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #007bff;
}

.loading-state p {
  margin: 0;
  font-size: 1rem;
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.broker-header {
  animation: fadeInUp 0.6s ease-out;
}

.broker-stats-grid {
  animation: fadeInUp 0.8s ease-out;
}

.broker-section {
  animation: fadeInUp 1s ease-out;
}

.stat-card {
  animation: slideInRight 0.6s ease-out;
}

.stat-card:nth-child(2) {
  animation-delay: 0.1s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.2s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.3s;
}
