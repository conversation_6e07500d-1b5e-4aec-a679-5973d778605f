/* Broker Dashboard - Black & White Theme */
.broker-dashboard {
  padding: 32px;
  width: 100%;
  min-height: calc(100vh - 80px);
  background: #ffffff;
  position: relative;
}

.broker-dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #ffffff;
}

/* Header */
.broker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background: #ffffff;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.broker-header-content h1 {
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  color: #000000;
}

.broker-header-content p {
  font-size: 16px;
  color: #333333;
  margin: 0;
  font-weight: 500;
}

.broker-header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.btn-broker-action {
  padding: 12px 24px;
  border: 2px solid #000000;
  background: #ffffff;
  color: #000000;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn-broker-action:hover {
  background: #000000;
  color: #ffffff;
  transform: translateY(-1px);
}

.btn-broker-action.primary {
  background: #000000;
  color: #ffffff;
}

.btn-broker-action.primary:hover {
  background: #333333;
}

/* Stats Grid */
.broker-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: #ffffff;
  border: 2px solid #000000;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background: #000000;
  color: #ffffff;
}

.stat-card .stat-icon i {
  font-size: 20px;
}

.stat-card .stat-content h3 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 4px 0;
  color: #000000;
}

.stat-card .stat-content p {
  font-size: 14px;
  color: #666666;
  margin: 0;
  font-weight: 500;
}

/* Content Grid */
.broker-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

/* Sections */
.broker-section {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
  padding-bottom: 16px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  color: #000000;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header h3 i {
  color: #666666;
  font-size: 16px;
}

.btn-broker-link {
  color: #000000;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  transition: all 0.2s ease;
  background: #ffffff;
  cursor: pointer;
}

.btn-broker-link:hover {
  background: #f8f9fa;
  border-color: #000000;
}

.section-content {
  padding: 24px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666666;
}

.loading-state i {
  font-size: 24px;
  margin-bottom: 12px;
  color: #000000;
}

.loading-state p {
  margin: 0;
  font-size: 14px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #666666;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cccccc;
}

.empty-state h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333333;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 20px 0;
  color: #666666;
}

/* Clients List */
.clients-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.client-item:hover {
  border-color: #e0e0e0;
  background: #fafafa;
}

.client-info h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #000000;
}

.client-info p {
  font-size: 14px;
  color: #666666;
  margin: 0 0 4px 0;
}

.client-status {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.client-value {
  text-align: right;
}

.client-value .value {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  display: block;
}

.client-value .period {
  font-size: 12px;
  color: #666666;
}

/* Deals List */
.deals-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.deal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.deal-item:hover {
  border-color: #e0e0e0;
  background: #fafafa;
}

.deal-info h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #000000;
}

.deal-info p {
  font-size: 14px;
  color: #666666;
  margin: 0 0 4px 0;
}

.deal-date {
  font-size: 12px;
  color: #999999;
}

.deal-commission {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.deal-commission .commission {
  font-size: 18px;
  font-weight: 700;
  color: #28a745;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

/* Quick Actions */
.quick-actions-section {
  grid-column: 1 / -1;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-top: 16px;
}

.action-btn {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 16px !important;
  padding: 40px 24px !important;
  border: 3px solid #e0e0e0 !important;
  border-radius: 16px !important;
  background: #ffffff !important;
  color: #333333 !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  min-height: 160px !important;
  text-align: center !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.action-btn:hover {
  border-color: #000000;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.action-btn.primary-action {
  border-color: #000000;
  background: #000000;
  color: #ffffff;
}

.action-btn.primary-action:hover {
  background: #333333;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
}

.action-btn i {
  font-size: 40px !important;
  margin-bottom: 0 !important;
  color: inherit !important;
}

.action-btn span {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  color: inherit;
}

/* Commission Metrics */
.commission-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.metric {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  font-weight: 500;
}

.metric-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #000000;
}

/* Pipeline Stages */
.pipeline-stages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stage {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.stage-label {
  display: block;
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  font-weight: 500;
}

.stage-count {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #000000;
}

/* Responsive */
@media (max-width: 768px) {
  .broker-dashboard {
    padding: 20px 16px;
  }

  .broker-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px 20px;
  }

  .broker-header-actions {
    width: 100%;
    justify-content: center;
  }

  .broker-stats-grid {
    grid-template-columns: 1fr;
  }

  .broker-content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .action-btn {
    min-height: 120px;
    padding: 28px 16px;
  }

  .action-btn i {
    font-size: 28px;
  }

  .commission-metrics {
    grid-template-columns: 1fr;
  }

  .pipeline-stages {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .broker-header-content h1 {
    font-size: 24px;
  }

  .stat-card {
    padding: 20px;
  }

  .broker-section {
    margin-bottom: 20px;
  }

  .section-header {
    padding: 20px 20px 0 20px;
  }

  .section-content {
    padding: 20px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .action-btn {
    min-height: 110px;
    padding: 24px 16px;
    gap: 12px;
  }

  .action-btn i {
    font-size: 24px;
  }

  .action-btn span {
    font-size: 13px;
  }

  .pipeline-stages {
    grid-template-columns: 1fr;
  }
}

.broker-dashboard-container .header-actions .btn-primary:hover {
  background: #fff;
  color: #000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Broker Stats Grid - Identical to dashboard-stats.css but with broker- prefix */
.broker-stats-container {
  width: 100%;
  margin-bottom: 25px;
  padding: 0;
  max-width: 100%;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.broker-stats-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px;
  padding: 0;
  width: 100%;
  align-items: stretch;
}

@media (max-width: 1200px) {
  .broker-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* New elegant card design */
.broker-stat-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.25s ease;
  cursor: pointer;
  position: relative;
  border: 1px solid #000;
  height: 140px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #000;
  overflow: hidden;
  width: 100%;
}

.broker-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  background: #f9f9f9;
}

/* Refined icon style */
.broker-stat-card-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  background: transparent;
}

.broker-stat-card-icon svg {
  width: 30px;
  height: 30px;
}

.broker-stat-card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.broker-stat-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 0 0 5px 0;
  letter-spacing: 0.3px;
}

.broker-stat-card-value {
  font-size: 42px;
  font-weight: 700;
  color: #000;
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.broker-stat-card-description {
  font-size: 13px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
  max-width: 90%;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background: #fff;
  border: 1px solid #000;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #111827;
}

.card-header h3 i {
  color: #000;
}

.btn-link {
  background: none;
  border: none;
  color: #000;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.btn-link:hover {
  color: #333;
}

.card-content {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}



/* Clients List */
.clients-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.client-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.client-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.client-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.client-status {
  color: #27ae60;
  font-size: 0.85rem;
  font-weight: 500;
}

.client-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.client-value .value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.client-value .period {
  font-size: 0.85rem;
  color: #6c757d;
}

/* Deals List */
.deals-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.deal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.deal-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.deal-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.deal-info p {
  margin: 0 0 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.deal-date {
  color: #000;
  font-size: 0.85rem;
  font-weight: 500;
}

.deal-commission {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.deal-commission .commission {
  font-size: 1.2rem;
  font-weight: 700;
  color: #27ae60;
}

.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}









/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .broker-dashboard-container {
    padding: 1rem;
    background: transparent;
    min-height: calc(100vh - 60px);
  }

  .broker-dashboard-container .dashboard-header {
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .broker-dashboard-container .header-content {
    text-align: center;
    width: 100%;
  }

  .broker-dashboard-container .header-content h1 {
    font-size: 1.6rem;
    justify-content: center;
  }

  .broker-dashboard-container .header-content h1 i {
    font-size: 1.4rem;
  }

  .broker-dashboard-container .header-actions {
    width: 100%;
    justify-content: center;
  }

  .broker-dashboard-container .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  /* Broker Stats Mobile Styles */
  .broker-stats-container {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
    width: calc(100% - 20px) !important;
    box-sizing: border-box !important;
  }

  .broker-stats-container .broker-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 15px !important;
    height: auto !important;
    min-height: 80px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: 1fr auto !important;
    gap: 15px !important;
    align-items: center !important;
    position: relative !important;
  }

  .broker-stats-container .broker-stat-card-content {
    grid-column: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 8px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 14px !important;
    line-height: 1.2 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 12px !important;
    line-height: 1.3 !important;
    word-wrap: break-word !important;
  }

  .broker-stats-container .broker-stat-card-value {
    grid-column: 2 !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin: 0 !important;
    line-height: 1 !important;
    position: static !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    height: auto !important;
    bottom: auto !important;
    right: auto !important;
  }

  .header-content h1 {
    font-size: 1.6rem;
  }

  .header-content h1 i {
    font-size: 1.4rem;
  }

  .header-actions .btn-primary {
    padding: 0.7rem 1.3rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 1.3rem;
  }

  .stat-content h3 {
    font-size: 1.8rem;
  }

  .dashboard-content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-header h3 {
    font-size: 1.1rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .client-item,
  .deal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .client-value,
  .deal-commission {
    width: 100%;
    align-items: flex-start;
  }



  .pipeline-stages {
    flex-direction: column;
    gap: 1rem;
  }

  .stage {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
  }

  .stage-label {
    font-size: 0.9rem;
    margin-bottom: 0;
  }

  .stage-count {
    font-size: 1.3rem;
  }
}

/* Small Mobile Screens */
@media (max-width: 480px) {
  .broker-dashboard-container {
    padding: 0.5rem;
  }

  .broker-dashboard-container .dashboard-header {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .broker-stats-container {
    padding: 0 5px !important;
    width: calc(100% - 10px) !important;
  }

  .broker-stats-container .broker-stat-card {
    padding: 12px !important;
    min-height: 70px !important;
    gap: 10px !important;
  }

  .broker-stats-container .broker-stat-card-icon {
    width: 28px !important;
    height: 28px !important;
  }

  .broker-stats-container .broker-stat-card-icon svg {
    width: 24px !important;
    height: 24px !important;
  }

  .broker-stats-container .broker-stat-card-title {
    font-size: 13px !important;
  }

  .broker-stats-container .broker-stat-card-description {
    font-size: 11px !important;
  }

  .broker-stats-container .broker-stat-card-value {
    font-size: 32px !important;
    min-width: 50px !important;
  }

  .header-content h1 {
    font-size: 1.3rem;
  }

  .stats-grid {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .stat-content h3 {
    font-size: 1.3rem;
  }

  .dashboard-content-grid {
    gap: 0.75rem;
  }

  .card-header {
    padding: 0.75rem;
  }

  .card-content {
    padding: 0.75rem;
  }

  .stage {
    padding: 0.5rem;
  }

  .stage-label {
    font-size: 0.8rem;
  }

  .stage-count {
    font-size: 1.1rem;
  }
}

/* Dashboard Summary Styles */
.dashboard-summary {
  margin: 2rem 0;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.summary-content h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.summary-stat {
  text-align: center;
}

.summary-stat .stat-value {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
}

.summary-stat .stat-label {
  display: block;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Loading State Styles */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6c757d;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #007bff;
}

.loading-state p {
  margin: 0;
  font-size: 1rem;
}
