import { v4 as uuidv4 } from 'uuid';
import { API_BASE_URL } from '../config/api-config';

/**
 * Service for handling energy bill uploads for the new simplified flow
 */

/**
 * Upload energy bills to S3 without requiring authentication
 * @param {File[]} files - Array of files to upload
 * @param {Object} clientData - Client information extracted from OCR
 * @returns {Promise} Promise object representing the upload result
 */
const uploadEnergyBills = async (files, clientData = {}) => {
  try {
    console.log('🔄 Starting energy bill upload process');
    console.log('📁 Files to upload:', files.length);

    // Generate a unique UUID for this upload session
    const uploadUUID = uuidv4();
    console.log('🆔 Generated upload UUID:', uploadUUID);

    // Simulate upload time for better UX
    await new Promise(resolve => setTimeout(resolve, 2000));

    // For now, return mock success response
    // TODO: Replace with actual API call when backend is ready
    const mockResult = {
      files: files.map((file, index) => ({
        originalName: file.name,
        s3Key: `energy-bills/${uploadUUID}/${Date.now()}-${file.name}`,
        s3Bucket: 'energy-app-uat-backend-files',
        fileSize: file.size,
        mimeType: file.type,
        uploadIndex: index
      }))
    };

    console.log('✅ Upload successful (mock):', mockResult);

    return {
      success: true,
      uploadUUID,
      files: mockResult.files,
      message: 'Energy bills uploaded successfully'
    };

  } catch (error) {
    console.error('❌ Error uploading energy bills:', error);
    throw new Error(`Failed to upload energy bills: ${error.message}`);
  }
};

/**
 * Extract data from uploaded energy bills using OCR
 * @param {string} uploadUUID - UUID of the upload session
 * @returns {Promise} Promise object representing the OCR result
 */
const extractBillData = async (uploadUUID) => {
  try {
    console.log('🔍 Starting OCR extraction for UUID:', uploadUUID);

    // Simulate OCR processing time
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock extracted data
    const mockExtractedData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      address: '123 Main Street, 75001 Paris, France',
      deliveryPointReference: 'PDL12345678901234',
      companyName: '', // Empty for individual clients
      clientType: 'individual'
    };

    console.log('✅ OCR extraction successful (mock):', mockExtractedData);

    return {
      success: true,
      extractedData: mockExtractedData,
      confidence: 0.95,
      message: 'Data extracted successfully'
    };

  } catch (error) {
    console.error('❌ Error extracting bill data:', error);
    throw new Error(`Failed to extract bill data: ${error.message}`);
  }
};

/**
 * Save client data and link it with uploaded files
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} clientData - Verified client data
 * @returns {Promise} Promise object representing the save result
 */
const saveClientData = async (uploadUUID, clientData) => {
  try {
    console.log('💾 Saving client data for UUID:', uploadUUID);
    console.log('📋 Client data:', clientData);

    // Simulate save time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Generate mock client ID
    const clientId = uuidv4();

    console.log('✅ Client data saved successfully (mock):', clientId);

    return {
      success: true,
      clientId,
      message: 'Client data saved successfully'
    };

  } catch (error) {
    console.error('❌ Error saving client data:', error);
    throw new Error(`Failed to save client data: ${error.message}`);
  }
};

/**
 * Get available appointment slots
 * @returns {Promise} Promise object representing available slots
 */
const getAvailableSlots = async () => {
  try {
    console.log('📅 Fetching available appointment slots');

    // Simulate API call time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Generate mock slots for the next few days
    const today = new Date();
    const mockSlots = [];

    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      // Skip weekends
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        const dateString = date.toISOString().split('T')[0];

        // Add morning slots
        mockSlots.push({ date: dateString, time: '09:00', available: true });
        mockSlots.push({ date: dateString, time: '10:30', available: true });

        // Add afternoon slots
        mockSlots.push({ date: dateString, time: '14:00', available: true });
        mockSlots.push({ date: dateString, time: '15:30', available: true });
      }
    }

    console.log('✅ Available slots fetched (mock):', mockSlots);

    return {
      success: true,
      slots: mockSlots
    };

  } catch (error) {
    console.error('❌ Error fetching appointment slots:', error);
    throw new Error(`Failed to fetch appointment slots: ${error.message}`);
  }
};

/**
 * Book an appointment
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} appointmentData - Appointment details
 * @returns {Promise} Promise object representing the booking result
 */
const bookAppointment = async (uploadUUID, appointmentData) => {
  try {
    console.log('📅 Booking appointment for UUID:', uploadUUID);
    console.log('📋 Appointment data:', appointmentData);

    // Simulate booking time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Generate mock appointment IDs
    const appointmentId = uuidv4();
    const calendarEventId = `event_${Date.now()}`;

    console.log('✅ Appointment booked successfully (mock)');

    return {
      success: true,
      appointmentId,
      calendarEventId,
      message: 'Appointment booked successfully'
    };

  } catch (error) {
    console.error('❌ Error booking appointment:', error);
    throw new Error(`Failed to book appointment: ${error.message}`);
  }
};

export default {
  uploadEnergyBills,
  extractBillData,
  saveClientData,
  getAvailableSlots,
  bookAppointment
};
