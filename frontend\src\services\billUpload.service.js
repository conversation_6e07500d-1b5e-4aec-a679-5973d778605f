import { v4 as uuidv4 } from 'uuid';
import { API_BASE_URL } from '../config/api-config';

/**
 * Service for handling energy bill uploads for the new simplified flow
 */

/**
 * Upload energy bills to S3 without requiring authentication
 * @param {File[]} files - Array of files to upload
 * @param {Object} clientData - Client information extracted from OCR
 * @returns {Promise} Promise object representing the upload result
 */
const uploadEnergyBills = async (files, clientData = {}) => {
  try {
    console.log('🔄 Starting energy bill upload process');
    console.log('📁 Files to upload:', files.length);

    // Generate a unique UUID for this upload session
    const uploadUUID = uuidv4();
    console.log('🆔 Generated upload UUID:', uploadUUID);

    // Create FormData for file upload
    const formData = new FormData();

    // Add files to FormData
    files.forEach((file, index) => {
      formData.append('energyBills', file);
      console.log(`📄 Added file ${index + 1}: ${file.name} (${file.size} bytes)`);
    });

    // Add metadata
    formData.append('uploadUUID', uploadUUID);
    formData.append('clientData', JSON.stringify(clientData));
    formData.append('uploadTimestamp', new Date().toISOString());

    console.log('🚀 Sending upload request to backend');

    try {
      // Make the upload request
      const response = await fetch(`${API_BASE_URL}/api/energy-bills/upload`, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header - let browser set it with boundary for FormData
      });

      console.log('📡 Upload response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Upload failed:', errorText);
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Upload successful:', result);

      return {
        success: true,
        uploadUUID,
        files: result.files,
        message: 'Energy bills uploaded successfully'
      };

    } catch (fetchError) {
      console.error('❌ Backend not available:', fetchError);

      // Fall back to mock response for development
      console.log('🔄 Using mock upload for development');

      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockResult = {
        files: files.map((file, index) => ({
          originalName: file.name,
          s3Key: `energy-bills/${uploadUUID}/${Date.now()}-${file.name}`,
          s3Bucket: 'energy-app-uat-backend-files',
          fileSize: file.size,
          mimeType: file.type,
          uploadIndex: index
        }))
      };

      return {
        success: true,
        uploadUUID,
        files: mockResult.files,
        message: 'Energy bills uploaded successfully (development mode)'
      };
    }

  } catch (error) {
    console.error('❌ Error uploading energy bills:', error);
    throw new Error(`Failed to upload energy bills: ${error.message}`);
  }
};

/**
 * Extract data from uploaded energy bills using OCR
 * @param {string} uploadUUID - UUID of the upload session
 * @returns {Promise} Promise object representing the OCR result
 */
const extractBillData = async (uploadUUID) => {
  try {
    console.log('🔍 Starting OCR extraction for UUID:', uploadUUID);

    // Call the real backend API for OCR extraction
    const response = await fetch(`${API_BASE_URL}/api/energy-bills/extract`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ uploadUUID }),
    });

    console.log('📡 OCR response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ OCR extraction failed:', errorData);
      throw new Error(errorData.message || `OCR extraction failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ OCR extraction successful:', result);

    return {
      success: true,
      extractedData: result.extractedData,
      confidence: result.confidence,
      message: result.message
    };

  } catch (error) {
    console.error('❌ Error extracting bill data:', error);

    // If the backend is not available, fall back to mock data for development
    if (error.message.includes('fetch')) {
      console.log('🔄 Backend not available, using mock data for development');

      const mockExtractedData = {
        firstName: '',
        lastName: '',
        email: '',
        address: '',
        deliveryPointReference: 'PDL12345678901234',
        companyName: 'EDF',
        clientType: 'individual',
        extractedInvoiceData: {
          invoiceNumber: 'FA123456789',
          provider: 'EDF',
          amount: '89.45',
          consumption: '1250',
          energyType: 'electricity'
        }
      };

      return {
        success: true,
        extractedData: mockExtractedData,
        confidence: 0.85,
        message: 'Data extracted successfully (development mode)'
      };
    }

    throw new Error(`Failed to extract bill data: ${error.message}`);
  }
};

/**
 * Save client data and link it with uploaded files
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} clientData - Verified client data
 * @returns {Promise} Promise object representing the save result
 */
const saveClientData = async (uploadUUID, clientData) => {
  try {
    console.log('💾 Saving client data for UUID:', uploadUUID);
    console.log('📋 Client data:', clientData);

    const response = await fetch(`${API_BASE_URL}/api/energy-bills/save-client`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uploadUUID,
        clientData,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to save client data');
    }

    const result = await response.json();
    console.log('✅ Client data saved successfully:', result);

    return result;

  } catch (error) {
    console.error('❌ Error saving client data:', error);
    throw new Error(`Failed to save client data: ${error.message}`);
  }
};

/**
 * Get available appointment slots
 * @returns {Promise} Promise object representing available slots
 */
const getAvailableSlots = async () => {
  try {
    console.log('📅 Fetching available appointment slots');

    // Simulate API call time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Generate mock slots for the next few days
    const today = new Date();
    const mockSlots = [];

    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      // Skip weekends
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        const dateString = date.toISOString().split('T')[0];

        // Add morning slots
        mockSlots.push({ date: dateString, time: '09:00', available: true });
        mockSlots.push({ date: dateString, time: '10:30', available: true });

        // Add afternoon slots
        mockSlots.push({ date: dateString, time: '14:00', available: true });
        mockSlots.push({ date: dateString, time: '15:30', available: true });
      }
    }

    console.log('✅ Available slots fetched (mock):', mockSlots);

    return {
      success: true,
      slots: mockSlots
    };

  } catch (error) {
    console.error('❌ Error fetching appointment slots:', error);
    throw new Error(`Failed to fetch appointment slots: ${error.message}`);
  }
};

/**
 * Book an appointment
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} appointmentData - Appointment details
 * @returns {Promise} Promise object representing the booking result
 */
const bookAppointment = async (uploadUUID, appointmentData) => {
  try {
    console.log('📅 Booking appointment for UUID:', uploadUUID);
    console.log('📋 Appointment data:', appointmentData);

    try {
      // Call the real backend API for appointment booking
      const response = await fetch(`${API_BASE_URL}/api/energy-bills/book-appointment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uploadUUID,
          appointmentData,
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Appointment booking failed:', errorData);
        throw new Error(errorData.message || `Appointment booking failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Appointment booked successfully:', result);

      return {
        success: true,
        appointmentId: result.appointmentId,
        calendarEventId: result.calendarEventId,
        calendarEventLink: result.calendarEventLink,
        meetingLink: result.meetingLink,
        message: result.message
      };

    } catch (fetchError) {
      console.error('❌ Backend not available for appointment booking:', fetchError);

      // Fall back to mock response for development
      console.log('🔄 Using mock appointment booking for development');

      await new Promise(resolve => setTimeout(resolve, 2000));

      const appointmentId = uuidv4();
      const calendarEventId = `mock_event_${Date.now()}`;

      return {
        success: true,
        appointmentId,
        calendarEventId,
        calendarEventLink: 'https://calendar.google.com/calendar/event?eid=mock',
        meetingLink: appointmentData.contactMethod === 'video' ? 'https://meet.google.com/mock-meeting' : null,
        message: 'Appointment booked successfully (development mode)'
      };
    }

  } catch (error) {
    console.error('❌ Error booking appointment:', error);
    throw new Error(`Failed to book appointment: ${error.message}`);
  }
};

export default {
  uploadEnergyBills,
  extractBillData,
  saveClientData,
  getAvailableSlots,
  bookAppointment
};
