import { v4 as uuidv4 } from 'uuid';
import { API_BASE_URL } from '../config/api-config';

/**
 * Service for handling energy bill uploads for the new simplified flow
 */

/**
 * Upload energy bills to S3 without requiring authentication
 * @param {File[]} files - Array of files to upload
 * @param {Object} clientData - Client information extracted from OCR
 * @returns {Promise} Promise object representing the upload result
 */
const uploadEnergyBills = async (files, clientData = {}) => {
  try {
    console.log('🔄 Starting energy bill upload process');
    console.log('📁 Files to upload:', files.length);
    
    // Generate a unique UUID for this upload session
    const uploadUUID = uuidv4();
    console.log('🆔 Generated upload UUID:', uploadUUID);

    // Create FormData for file upload
    const formData = new FormData();
    
    // Add files to FormData
    files.forEach((file, index) => {
      formData.append('energyBills', file);
      console.log(`📄 Added file ${index + 1}: ${file.name} (${file.size} bytes)`);
    });

    // Add metadata
    formData.append('uploadUUID', uploadUUID);
    formData.append('clientData', JSON.stringify(clientData));
    formData.append('uploadTimestamp', new Date().toISOString());

    console.log('🚀 Sending upload request to backend');

    // Make the upload request
    const response = await fetch(`${API_BASE_URL}/api/energy-bills/upload`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let browser set it with boundary for FormData
    });

    console.log('📡 Upload response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Upload failed:', errorText);
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Upload successful:', result);

    return {
      success: true,
      uploadUUID,
      files: result.files,
      message: 'Energy bills uploaded successfully'
    };

  } catch (error) {
    console.error('❌ Error uploading energy bills:', error);
    throw new Error(`Failed to upload energy bills: ${error.message}`);
  }
};

/**
 * Extract data from uploaded energy bills using OCR
 * @param {string} uploadUUID - UUID of the upload session
 * @returns {Promise} Promise object representing the OCR result
 */
const extractBillData = async (uploadUUID) => {
  try {
    console.log('🔍 Starting OCR extraction for UUID:', uploadUUID);

    const response = await fetch(`${API_BASE_URL}/api/energy-bills/extract`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ uploadUUID }),
    });

    console.log('📡 OCR response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ OCR extraction failed:', errorText);
      throw new Error(`OCR extraction failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ OCR extraction successful:', result);

    return {
      success: true,
      extractedData: result.extractedData,
      confidence: result.confidence,
      message: 'Data extracted successfully'
    };

  } catch (error) {
    console.error('❌ Error extracting bill data:', error);
    throw new Error(`Failed to extract bill data: ${error.message}`);
  }
};

/**
 * Save client data and link it with uploaded files
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} clientData - Verified client data
 * @returns {Promise} Promise object representing the save result
 */
const saveClientData = async (uploadUUID, clientData) => {
  try {
    console.log('💾 Saving client data for UUID:', uploadUUID);
    console.log('📋 Client data:', clientData);

    const response = await fetch(`${API_BASE_URL}/api/energy-bills/save-client`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uploadUUID,
        clientData,
        timestamp: new Date().toISOString()
      }),
    });

    console.log('📡 Save response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Save failed:', errorText);
      throw new Error(`Save failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Client data saved successfully:', result);

    return {
      success: true,
      clientId: result.clientId,
      message: 'Client data saved successfully'
    };

  } catch (error) {
    console.error('❌ Error saving client data:', error);
    throw new Error(`Failed to save client data: ${error.message}`);
  }
};

/**
 * Get available appointment slots
 * @returns {Promise} Promise object representing available slots
 */
const getAvailableSlots = async () => {
  try {
    console.log('📅 Fetching available appointment slots');

    const response = await fetch(`${API_BASE_URL}/api/energy-bills/appointment-slots`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch slots: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Available slots fetched:', result);

    return {
      success: true,
      slots: result.slots
    };

  } catch (error) {
    console.error('❌ Error fetching appointment slots:', error);
    throw new Error(`Failed to fetch appointment slots: ${error.message}`);
  }
};

/**
 * Book an appointment
 * @param {string} uploadUUID - UUID of the upload session
 * @param {Object} appointmentData - Appointment details
 * @returns {Promise} Promise object representing the booking result
 */
const bookAppointment = async (uploadUUID, appointmentData) => {
  try {
    console.log('📅 Booking appointment for UUID:', uploadUUID);
    console.log('📋 Appointment data:', appointmentData);

    const response = await fetch(`${API_BASE_URL}/api/energy-bills/book-appointment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uploadUUID,
        appointmentData,
        timestamp: new Date().toISOString()
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Appointment booking failed:', errorText);
      throw new Error(`Appointment booking failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Appointment booked successfully:', result);

    return {
      success: true,
      appointmentId: result.appointmentId,
      calendarEventId: result.calendarEventId,
      message: 'Appointment booked successfully'
    };

  } catch (error) {
    console.error('❌ Error booking appointment:', error);
    throw new Error(`Failed to book appointment: ${error.message}`);
  }
};

export default {
  uploadEnergyBills,
  extractBillData,
  saveClientData,
  getAvailableSlots,
  bookAppointment
};
