import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import { showErrorMessage, showSuccessMessage } from '../utils/toastNotifications';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/clients.css';

const CompareOffers = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [offers, setOffers] = useState([]);
  const [selectedOffers, setSelectedOffers] = useState([]);
  const [comparisonCriteria, setComparisonCriteria] = useState({
    energyType: 'Electricity',
    consumption: '',
    contractDuration: '12',
    clientType: 'Business'
  });
  const [showComparison, setShowComparison] = useState(false);

  useEffect(() => {
    fetchOffers();
  }, [comparisonCriteria]);

  const fetchOffers = async () => {
    try {
      setLoading(true);
      logger.info('Fetching energy offers for comparison...');
      
      // Mock offers data based on criteria
      const mockOffers = [
        {
          id: '1',
          supplier: 'EDF',
          offerName: 'Business Pro',
          energyType: 'Electricity',
          pricePerKwh: 0.15,
          fixedCost: 25,
          contractDuration: 12,
          greenEnergy: false,
          commission: 5,
          features: ['24/7 Support', 'Online Management', 'Monthly Billing'],
          rating: 4.2,
          estimatedMonthlyCost: 180,
          estimatedAnnualSavings: 240
        },
        {
          id: '2',
          supplier: 'Engie',
          offerName: 'Green Business',
          energyType: 'Electricity',
          pricePerKwh: 0.16,
          fixedCost: 20,
          contractDuration: 12,
          greenEnergy: true,
          commission: 4.5,
          features: ['100% Green Energy', 'Carbon Offset', 'Energy Audit'],
          rating: 4.5,
          estimatedMonthlyCost: 185,
          estimatedAnnualSavings: 180
        },
        {
          id: '3',
          supplier: 'Total Energies',
          offerName: 'Flex Business',
          energyType: 'Electricity',
          pricePerKwh: 0.14,
          fixedCost: 30,
          contractDuration: 12,
          greenEnergy: false,
          commission: 5.5,
          features: ['Flexible Pricing', 'Peak/Off-peak Rates', 'Smart Meter'],
          rating: 4.0,
          estimatedMonthlyCost: 175,
          estimatedAnnualSavings: 300
        },
        {
          id: '4',
          supplier: 'Vattenfall',
          offerName: 'Eco Pro',
          energyType: 'Electricity',
          pricePerKwh: 0.17,
          fixedCost: 15,
          contractDuration: 12,
          greenEnergy: true,
          commission: 4,
          features: ['100% Renewable', 'Energy Efficiency Tips', 'Mobile App'],
          rating: 4.3,
          estimatedMonthlyCost: 190,
          estimatedAnnualSavings: 120
        }
      ];

      setOffers(mockOffers);
      logger.info('Energy offers fetched successfully');
    } catch (error) {
      logger.error('Error fetching energy offers:', error);
      showErrorMessage('FETCH_OFFERS_ERROR', 'Failed to load energy offers');
    } finally {
      setLoading(false);
    }
  };

  const handleCriteriaChange = (e) => {
    const { name, value } = e.target;
    setComparisonCriteria(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOfferSelect = (offerId) => {
    setSelectedOffers(prev => {
      if (prev.includes(offerId)) {
        return prev.filter(id => id !== offerId);
      } else if (prev.length < 3) {
        return [...prev, offerId];
      } else {
        showErrorMessage('SELECTION_LIMIT', 'You can compare up to 3 offers at a time');
        return prev;
      }
    });
  };

  const handleCompareSelected = () => {
    if (selectedOffers.length < 2) {
      showErrorMessage('SELECTION_ERROR', 'Please select at least 2 offers to compare');
      return;
    }
    setShowComparison(true);
  };

  const handleSelectOffer = (offerId) => {
    const selectedOffer = offers.find(offer => offer.id === offerId);
    showSuccessMessage('OFFER_SELECTED', `${selectedOffer.supplier} ${selectedOffer.offerName} selected for client`);
    // Here you would typically navigate to a deal creation page or modal
    navigate('/add-client');
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getSelectedOffers = () => {
    return offers.filter(offer => selectedOffers.includes(offer.id));
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="fas fa-star"></i>);
    }
    if (hasHalfStar) {
      stars.push(<i key="half" className="fas fa-star-half-alt"></i>);
    }
    for (let i = stars.length; i < 5; i++) {
      stars.push(<i key={i} className="far fa-star"></i>);
    }
    return stars;
  };

  return (
    <DashboardLayout>
      <div className="clients-container">
        <div className="clients-header">
          <div className="header-content">
            <h1>
              <i className="fas fa-balance-scale"></i>
              Compare Energy Offers
            </h1>
            <p>Find the best energy deals for your clients</p>
          </div>
          <div className="header-actions">
            {selectedOffers.length > 0 && (
              <button className="btn-primary" onClick={handleCompareSelected}>
                <i className="fas fa-balance-scale"></i>
                Compare Selected ({selectedOffers.length})
              </button>
            )}
          </div>
        </div>

        {/* Comparison Criteria */}
        <div className="comparison-criteria">
          <h3>Comparison Criteria</h3>
          <div className="criteria-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="energyType">Energy Type</label>
                <select
                  id="energyType"
                  name="energyType"
                  value={comparisonCriteria.energyType}
                  onChange={handleCriteriaChange}
                >
                  <option value="Electricity">Electricity</option>
                  <option value="Gas">Gas</option>
                  <option value="Both">Both</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="consumption">Monthly Consumption (kWh)</label>
                <input
                  type="number"
                  id="consumption"
                  name="consumption"
                  value={comparisonCriteria.consumption}
                  onChange={handleCriteriaChange}
                  placeholder="1000"
                  min="0"
                />
              </div>
              <div className="form-group">
                <label htmlFor="contractDuration">Contract Duration (months)</label>
                <select
                  id="contractDuration"
                  name="contractDuration"
                  value={comparisonCriteria.contractDuration}
                  onChange={handleCriteriaChange}
                >
                  <option value="12">12 months</option>
                  <option value="24">24 months</option>
                  <option value="36">36 months</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="clientType">Client Type</label>
                <select
                  id="clientType"
                  name="clientType"
                  value={comparisonCriteria.clientType}
                  onChange={handleCriteriaChange}
                >
                  <option value="Business">Business</option>
                  <option value="Individual">Individual</option>
                  <option value="Industrial">Industrial</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="loading-container">
            <Spinner size="large" message="Loading offers..." />
          </div>
        ) : (
          <>
            {/* Offers Grid */}
            <div className="offers-grid">
              {offers.map((offer) => (
                <div 
                  key={offer.id} 
                  className={`offer-card ${selectedOffers.includes(offer.id) ? 'selected' : ''}`}
                >
                  <div className="offer-header">
                    <div className="supplier-info">
                      <h3>{offer.supplier}</h3>
                      <p>{offer.offerName}</p>
                    </div>
                    <div className="offer-rating">
                      <div className="stars">
                        {renderStars(offer.rating)}
                      </div>
                      <span className="rating-value">{offer.rating}</span>
                    </div>
                  </div>

                  <div className="offer-pricing">
                    <div className="price-item">
                      <span className="label">Price per kWh:</span>
                      <span className="value">{formatCurrency(offer.pricePerKwh)}</span>
                    </div>
                    <div className="price-item">
                      <span className="label">Fixed Cost:</span>
                      <span className="value">{formatCurrency(offer.fixedCost)}/month</span>
                    </div>
                    <div className="price-item highlight">
                      <span className="label">Est. Monthly Cost:</span>
                      <span className="value">{formatCurrency(offer.estimatedMonthlyCost)}</span>
                    </div>
                  </div>

                  <div className="offer-features">
                    <h4>Features</h4>
                    <ul>
                      {offer.features.map((feature, index) => (
                        <li key={index}>
                          <i className="fas fa-check"></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="offer-details">
                    <div className="detail-item">
                      <span className="label">Commission:</span>
                      <span className="value commission">{offer.commission}%</span>
                    </div>
                    {offer.greenEnergy && (
                      <div className="green-badge">
                        <i className="fas fa-leaf"></i>
                        Green Energy
                      </div>
                    )}
                    <div className="savings">
                      <span className="label">Est. Annual Savings:</span>
                      <span className="value">{formatCurrency(offer.estimatedAnnualSavings)}</span>
                    </div>
                  </div>

                  <div className="offer-actions">
                    <button 
                      className={`btn-secondary ${selectedOffers.includes(offer.id) ? 'selected' : ''}`}
                      onClick={() => handleOfferSelect(offer.id)}
                    >
                      {selectedOffers.includes(offer.id) ? (
                        <>
                          <i className="fas fa-check"></i>
                          Selected
                        </>
                      ) : (
                        <>
                          <i className="fas fa-plus"></i>
                          Select to Compare
                        </>
                      )}
                    </button>
                    <button 
                      className="btn-primary"
                      onClick={() => handleSelectOffer(offer.id)}
                    >
                      <i className="fas fa-handshake"></i>
                      Choose This Offer
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Comparison Table */}
            {showComparison && selectedOffers.length > 1 && (
              <div className="comparison-table-container">
                <div className="comparison-header">
                  <h3>Offer Comparison</h3>
                  <button 
                    className="btn-secondary"
                    onClick={() => setShowComparison(false)}
                  >
                    <i className="fas fa-times"></i>
                    Close Comparison
                  </button>
                </div>
                <div className="comparison-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Feature</th>
                        {getSelectedOffers().map(offer => (
                          <th key={offer.id}>{offer.supplier} {offer.offerName}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Price per kWh</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id}>{formatCurrency(offer.pricePerKwh)}</td>
                        ))}
                      </tr>
                      <tr>
                        <td>Fixed Monthly Cost</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id}>{formatCurrency(offer.fixedCost)}</td>
                        ))}
                      </tr>
                      <tr>
                        <td>Est. Monthly Total</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id} className="highlight">{formatCurrency(offer.estimatedMonthlyCost)}</td>
                        ))}
                      </tr>
                      <tr>
                        <td>Commission Rate</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id} className="commission">{offer.commission}%</td>
                        ))}
                      </tr>
                      <tr>
                        <td>Green Energy</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id}>
                            {offer.greenEnergy ? (
                              <span className="green-badge">
                                <i className="fas fa-leaf"></i>
                                Yes
                              </span>
                            ) : (
                              <span>No</span>
                            )}
                          </td>
                        ))}
                      </tr>
                      <tr>
                        <td>Rating</td>
                        {getSelectedOffers().map(offer => (
                          <td key={offer.id}>
                            <div className="stars">
                              {renderStars(offer.rating)}
                            </div>
                          </td>
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default CompareOffers;
