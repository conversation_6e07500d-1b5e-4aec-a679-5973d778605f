/* Bill Upload Section Styles */
html {
  scroll-behavior: smooth;
}

.bill-upload-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 40px 0;
  margin: 0;
}

.bill-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Compact Progress Indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px auto 30px auto;
  max-width: 600px;
  padding: 16px 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  position: relative;
}

/* Simple Progress List */
.progress-list {
  display: flex;
  align-items: center;
  gap: 32px;
  justify-content: center;
  flex-wrap: wrap;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  position: relative;
}

.progress-item.active {
  color: #000000;
  font-weight: 600;
}

.progress-item.completed {
  color: #10b981;
  font-weight: 600;
}

.progress-item::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e2e8f0;
  flex-shrink: 0;
}

.progress-item.active::before {
  background: #000000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.progress-item.completed::before {
  background: #10b981;
  content: '';
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 10px;
}

.progress-item:not(:last-child)::after {
  content: '→';
  position: absolute;
  right: -20px;
  color: #cbd5e1;
  font-size: 12px;
}

/* Upload Spinner */
.upload-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled button state during upload */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Upload zone disabled state during upload */
.upload-zone.uploading {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  background-color: #f8f9fa;
}

.upload-zone.uploading::after {
  content: 'Uploading...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10;
}

/* Remove old stepper connecting lines */



/* Old stepper styles removed - using new progress list */

.bill-upload-header {
  text-align: center;
  margin-bottom: 30px;
}

.bill-upload-title {
  font-size: 36px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
  letter-spacing: -0.025em;
}

.bill-upload-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.bill-upload-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  margin-bottom: 24px;
}

.error-icon {
  color: #ef4444;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-content {
  flex: 1;
}

.error-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.error-content p {
  font-size: 14px;
  color: #7f1d1d;
  margin: 0 0 12px 0;
}

.error-content .btn {
  font-size: 12px;
  padding: 6px 12px;
}

/* Upload Step */
.upload-step {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.upload-benefits {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  background: #000000;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.benefit-item span {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

/* File Uploader */
.file-uploader {
  width: 100%;
}

.upload-zone {
  border: 3px dashed #cbd5e1;
  border-radius: 16px;
  padding: 60px 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
  position: relative;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: #000000;
  background: #f1f5f9;
  transform: translateY(-2px);
}

.upload-icon {
  color: #64748b;
  margin-bottom: 20px;
}

.upload-text h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.upload-text p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 8px 0;
}

.browse-link {
  color: #000000;
  font-weight: 600;
  text-decoration: underline;
}

.upload-text small {
  font-size: 14px;
  color: #94a3b8;
}

/* Files Selected */
.files-selected {
  margin-top: 24px;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.files-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.clear-all-btn {
  background: transparent;
  color: #ef4444;
  border: 1px solid #ef4444;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: #ef4444;
  color: white;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.file-item:hover {
  background: #f1f5f9;
}

.file-icon {
  color: #64748b;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  word-break: break-word;
}

.file-size {
  font-size: 12px;
  color: #64748b;
}

.remove-file-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-file-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* Upload Info */
.upload-info {
  text-align: center;
}

.upload-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.file-types {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.file-type {
  background: #000000;
  color: white;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.upload-note {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

/* Processing Step */
.processing-step {
  text-align: center;
  padding: 60px 40px;
}

.processing-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.processing-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-animation h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.processing-animation p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.processing-steps {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.processing-step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.processing-step-item.active {
  opacity: 1;
}

.processing-step-item .step-number {
  width: 32px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #64748b;
}

.processing-step-item.active .step-number {
  background: #000000;
  color: white;
}

/* Review Step */
.review-step {
  text-align: center;
  padding: 60px 40px;
}

.review-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.success-icon {
  color: #10b981;
}

.review-success h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.review-success p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  max-width: 400px;
}

.next-steps {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

/* Footer */
.bill-upload-footer {
  margin-top: 40px;
  text-align: center;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-black {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.btn-black:hover {
  background: #1f2937;
  border-color: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: #000000;
  border-color: #000000;
}

.btn-outline:hover {
  background: #000000;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .bill-upload-section {
    padding: 40px 0;
  }

  .bill-upload-title {
    font-size: 32px;
    line-height: 1.2;
    margin-bottom: 16px;
  }

  .bill-upload-subtitle {
    font-size: 16px;
    line-height: 1.4;
  }

  .bill-upload-content {
    padding: 20px 16px;
    margin: 0 8px;
  }

  .upload-zone {
    padding: 32px 16px;
  }

  .upload-benefits {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .upload-actions,
  .next-steps {
    flex-direction: column;
    gap: 12px;
  }

  .processing-steps {
    flex-direction: column;
    gap: 12px;
  }

  .security-badges {
    flex-direction: column;
    gap: 12px;
  }

  /* Progress Indicator Mobile */
  .progress-indicator {
    max-width: 100%;
    padding: 16px 20px;
    margin: 0 16px;
  }

  .progress-step:nth-child(1)::after,
  .progress-step:nth-child(2)::after {
    width: 60px;
    left: 30px;
  }

  .progress-indicator .step-label {
    font-size: 11px;
    font-weight: 600;
    margin-top: 8px;
  }

  .progress-indicator .step-number {
    width: 32px;
    height: 32px;
    font-size: 13px;
    border-width: 2px;
  }

  /* Data Extraction Mobile */
  .data-extraction-step {
    padding: 20px 16px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .data-form {
    padding: 20px 16px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .form-group input,
  .form-group select {
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;
    padding: 20px 16px;
  }

  .form-actions .btn {
    width: 100%;
    min-height: 48px;
  }

  /* Authorization Section Mobile */
  .authorization-section {
    padding: 20px 16px;
    margin: 16px;
  }

  .authorization-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .authorization-checkbox input[type="checkbox"] {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  .authorization-checkbox label {
    font-size: 14px;
    line-height: 1.5;
    flex: 1;
  }

  /* Appointment Booking Mobile */
  .appointment-step {
    padding: 20px 16px;
  }

  .appointment-header {
    text-align: center;
    margin-bottom: 24px;
  }

  .appointment-header h3 {
    font-size: 24px;
    line-height: 1.3;
    margin-bottom: 12px;
  }

  .appointment-header p {
    font-size: 15px;
    line-height: 1.4;
  }

  .form-section {
    padding: 20px 16px;
    margin: 0 8px 16px 8px;
  }

  .slots-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .appointment-actions {
    flex-direction: column;
    gap: 12px;
    padding: 20px 16px;
  }

  .appointment-actions .btn {
    min-width: auto;
    width: 100%;
    min-height: 48px;
  }

  /* Client Summary Card Mobile */
  .client-summary-card {
    margin: 0 16px 20px 16px;
    padding: 20px 16px;
  }

  .client-summary-card .card-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .client-summary-card .client-avatar {
    width: 48px;
    height: 48px;
    align-self: center;
  }

  .client-summary-card .client-details h4 {
    font-size: 18px;
    margin-bottom: 6px;
  }

  .client-summary-card .client-details p {
    font-size: 14px;
    margin-bottom: 8px;
  }

  /* Contact Method Selection Mobile */
  .method-selection {
    margin: 0 16px;
  }

  .method-option {
    margin-bottom: 16px;
  }

  .method-content {
    padding: 16px;
  }

  .method-header {
    margin-bottom: 12px;
  }

  .method-header h4 {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .method-description {
    font-size: 13px;
    line-height: 1.4;
  }

  .method-duration {
    font-size: 12px;
    margin-top: 8px;
  }

  /* Summary Items Mobile */
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .summary-item:last-child {
    border-bottom: none;
  }

  .summary-label {
    min-width: auto;
    font-size: 13px;
    font-weight: 600;
    color: #64748b;
  }

  .summary-value {
    font-size: 14px;
    color: #1e293b;
  }

  /* Notes Section Mobile */
  .notes-section {
    margin: 0 16px;
  }

  .notes-section textarea {
    min-height: 100px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.4;
  }

  /* Trust Indicators Mobile */
  .trust-indicators {
    flex-direction: column;
    gap: 12px;
    margin: 20px 16px;
    padding: 16px;
  }

  .trust-indicator {
    font-size: 13px;
  }

  .appointment-form {
    padding: 20px;
  }

  .contact-methods {
    gap: 8px;
  }

  .contact-method {
    padding: 12px;
  }

  /* Completion Mobile */
  .completion-content h2 {
    font-size: 28px;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .completion-actions {
    flex-direction: column;
  }

  .social-proof {
    padding: 20px;
  }

  /* Client Type Toggle Mobile */
  .client-type-toggle {
    margin: 0 16px 20px 16px;
  }

  .client-type-toggle .toggle-option {
    flex: 1;
    min-height: 48px;
    font-size: 14px;
  }

  /* Better spacing for mobile */
  .step-content {
    padding: 0;
  }

  .step-content > * {
    margin-bottom: 20px;
  }

  .step-content > *:last-child {
    margin-bottom: 0;
  }
}

/* Data Extraction Step */
.extraction-step {
  padding: 20px 0;
}

.extraction-header {
  text-align: center;
  margin-bottom: 20px;
}

.extraction-header h3 {
  font-size: 22px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 6px 0;
}

.extraction-header p {
  font-size: 15px;
  color: #64748b;
  margin: 0;
}

.extraction-processing {
  text-align: center;
  padding: 20px 16px;
}

.uploaded-files-summary {
  margin-top: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.uploaded-files-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.files-list-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-summary-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.file-summary-item .file-icon {
  font-size: 16px;
}

.file-summary-item .file-name {
  flex: 1;
  font-size: 14px;
  color: #1e293b;
}

.processing-status {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.extraction-error {
  text-align: center;
  padding: 40px 20px;
}

.extraction-error .error-icon {
  color: #ef4444;
  margin-bottom: 16px;
}

.extraction-error h3 {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.extraction-error p {
  font-size: 16px;
  color: #7f1d1d;
  margin: 0 0 24px 0;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.extraction-results {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.data-form {
  background: #f8fafc;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-group input.editing,
.form-group textarea.editing {
  border-color: #f59e0b;
  background: #fffbeb;
}

.field-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.authorization-section {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
  margin: 24px 0;
}

.authorization-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.authorization-checkbox input[type="checkbox"] {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  cursor: pointer;
}

.authorization-checkbox label {
  font-size: 14px;
  line-height: 1.5;
  color: #92400e;
  cursor: pointer;
  flex: 1;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  margin-top: 32px;
}

.extraction-confidence {
  text-align: center;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.confidence-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.confidence-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.confidence-value {
  font-size: 18px;
  font-weight: 700;
  color: #15803d;
}

.confidence-note {
  font-size: 12px;
  color: #166534;
  margin: 0;
}

/* Appointment Booking Step */
.appointment-step {
  padding: 20px 0;
}

.appointment-header {
  text-align: center;
  margin-bottom: 32px;
}

.appointment-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.appointment-header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.appointment-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.client-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.client-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.client-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #374151;
}

.appointment-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.slot-card {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  background: white;
}

.slot-card:hover {
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slot-card.selected {
  border-color: #000000;
  background: #f8fafc;
}

.slot-date {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.slot-time {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8px;
}

.slot-status {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.contact-method:hover {
  border-color: #cbd5e1;
}

.contact-method input[type="radio"]:checked + .method-info {
  color: #000000;
}

.contact-method input[type="radio"]:checked {
  accent-color: #000000;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.method-icon {
  font-size: 24px;
}

.method-details h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.method-details p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.appointment-summary {
  margin-top: 24px;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.selected-appointment h4 {
  font-size: 16px;
  font-weight: 600;
  color: #166534;
  margin: 0 0 12px 0;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #15803d;
  font-weight: 600;
}

.appointment-error {
  text-align: center;
  padding: 40px 20px;
}

.appointment-error .error-icon {
  color: #ef4444;
  margin-bottom: 16px;
}

.appointment-error h3 {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.appointment-error p {
  font-size: 16px;
  color: #7f1d1d;
  margin: 0 0 24px 0;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* Completion Step */
.completion-step {
  padding: 40px 0;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px;
  margin: 20px 0;
}

.completion-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.success-animation {
  text-align: center;
  padding: 20px;
}

.success-icon {
  color: #10b981;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3));
}

.completion-content h2 {
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0;
}

.success-message {
  font-size: 20px;
  color: #475569;
  margin: 0;
  line-height: 1.6;
  font-weight: 500;
}

.completion-summary {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.summary-section h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
}

.next-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  text-align: left;
}

.step-item .step-number {
  width: 32px;
  height: 32px;
  background: #000000;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.step-content p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.appointment-details-card {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 16px;
  padding: 24px;
}

.appointment-details-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #166534;
  margin: 0 0 16px 0;
}

.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #bbf7d0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #15803d;
  font-weight: 600;
}

.calendar-link,
.meeting-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  transition: all 0.2s ease;
  display: inline-block;
}

.calendar-link:hover,
.meeting-link:hover {
  background: #dbeafe;
  border-color: #93c5fd;
  transform: translateY(-1px);
}

.meeting-link {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #15803d;
}

.meeting-link:hover {
  background: #dcfce7;
  border-color: #86efac;
}

.important-notes {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 16px;
  padding: 24px;
}

.important-notes h3 {
  font-size: 20px;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 16px 0;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.note-icon {
  font-size: 16px;
  margin-top: 2px;
}

.note-item p {
  font-size: 14px;
  color: #92400e;
  margin: 0;
  line-height: 1.5;
}

.completion-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 20px;
}

.social-proof {
  background: #f8fafc;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.testimonial {
  text-align: center;
  margin-bottom: 32px;
}

.testimonial p {
  font-size: 16px;
  font-style: italic;
  color: #374151;
  margin: 0 0 8px 0;
  line-height: 1.6;
}

.testimonial cite {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Client Type Toggle Styles */
.client-type-toggle {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  width: fit-content;
}

.toggle-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0;
}

.toggle-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.toggle-btn.active {
  background: #000000;
  color: #ffffff;
}

.toggle-btn:first-child {
  border-right: 1px solid #e2e8f0;
}

.toggle-btn.active:first-child {
  border-right: 1px solid #000000;
}

/* Compact Appointment Booking Styles */
.appointment-step {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 16px;
}

.appointment-header {
  text-align: center;
  margin-bottom: 24px;
}

.appointment-header .header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #000000 0%, #374151 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px auto;
  color: white;
}

.appointment-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
  line-height: 1.3;
}

.appointment-header p {
  font-size: 16px;
  color: #64748b;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.5;
}

.appointment-content {
  display: grid;
  gap: 20px;
}

/* Client Summary Card */
.client-summary-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.client-summary-card .card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.client-summary-card .client-avatar {
  width: 56px;
  height: 56px;
  background: #000000;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.client-summary-card .client-details h4 {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.client-summary-card .client-details p {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 8px;
}

.client-summary-card .client-type {
  display: inline-block;
  background: #000000;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Form Sections */
.appointment-form {
  display: grid;
  gap: 20px;
}

.form-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section .section-header {
  margin-bottom: 16px;
}

.form-section .section-header h4 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.form-section .section-header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* Time Slots */
.slots-container {
  margin-top: 24px;
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.slot-card {
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.slot-card:hover:not(.disabled) {
  border-color: #000000;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.slot-card.selected {
  border-color: #000000;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.slot-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8fafc;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.slot-date {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
}

.slot-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.slot-status.available {
  color: #10b981;
}

.slot-status.unavailable {
  color: #ef4444;
}

.slot-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.slot-time {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8px;
}

.slot-duration {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.selected-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  background: #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.no-slots-message {
  text-align: center;
  padding: 48px 24px;
  color: #64748b;
}

.no-slots-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-slots-message h5 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

/* Contact Methods */
.contact-methods {
  display: grid;
  gap: 16px;
  margin-top: 24px;
}

.contact-method-card {
  display: block;
  cursor: pointer;
}

.contact-method-card input[type="radio"] {
  display: none;
}

.method-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.contact-method-card:hover .method-content {
  border-color: #000000;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-method-card.selected .method-content {
  border-color: #000000;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.method-icon {
  width: 48px;
  height: 48px;
  background: #f1f5f9;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  flex-shrink: 0;
}

.contact-method-card.selected .method-icon {
  background: #000000;
  color: white;
}

.method-details {
  flex: 1;
}

.method-details h5 {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.method-details p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.method-duration {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.method-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
}

/* Custom Textarea */
.custom-textarea {
  width: 100%;
  min-height: 120px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s ease;
  background: #ffffff;
}

.custom-textarea:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.custom-textarea::placeholder {
  color: #94a3b8;
}

/* Appointment Summary Card */
.appointment-summary-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 16px;
  padding: 32px;
  margin-top: 8px;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.summary-icon {
  width: 48px;
  height: 48px;
  background: #0ea5e9;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.summary-header h4 {
  font-size: 20px;
  font-weight: 700;
  color: #0c4a6e;
  margin: 0;
}

.summary-content {
  display: grid;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
}

.summary-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #0c4a6e;
  min-width: 140px;
}

.summary-value {
  font-weight: 600;
  color: #1e293b;
  flex: 1;
}

/* Action Buttons */
.appointment-actions {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid #e2e8f0;
}

.appointment-actions .btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 180px;
  justify-content: center;
}

.appointment-actions .btn-large {
  padding: 18px 36px;
  font-size: 18px;
}

.appointment-actions .btn:hover:not(.btn-disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error States */
.appointment-error {
  text-align: center;
  padding: 80px 40px;
}

.error-icon {
  color: #ef4444;
  margin-bottom: 24px;
}

.appointment-error h3 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
}

.appointment-error p {
  font-size: 18px;
  color: #64748b;
  margin-bottom: 40px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.error-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}


