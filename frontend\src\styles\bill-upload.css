/* Bill Upload Section Styles */

.bill-upload-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 80px 0;
  margin: 0;
}

.bill-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  gap: 40px;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  left: 100%;
  width: 40px;
  height: 2px;
  background: #e2e8f0;
  z-index: 1;
}

.progress-step.completed:not(:last-child)::after {
  background: #000000;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  background: #e2e8f0;
  color: #64748b;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.progress-step.active .step-number {
  background: #000000;
  color: #ffffff;
}

.progress-step.completed .step-number {
  background: #10b981;
  color: #ffffff;
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  text-align: center;
}

.progress-step.active .step-label {
  color: #1e293b;
  font-weight: 600;
}

.progress-step.completed .step-label {
  color: #10b981;
  font-weight: 600;
}

.bill-upload-header {
  text-align: center;
  margin-bottom: 60px;
}

.bill-upload-title {
  font-size: 48px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
  letter-spacing: -0.025em;
}

.bill-upload-subtitle {
  font-size: 20px;
  color: #64748b;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.bill-upload-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  margin-bottom: 24px;
}

.error-icon {
  color: #ef4444;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-content {
  flex: 1;
}

.error-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.error-content p {
  font-size: 14px;
  color: #7f1d1d;
  margin: 0 0 12px 0;
}

.error-content .btn {
  font-size: 12px;
  padding: 6px 12px;
}

/* Upload Step */
.upload-step {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.upload-benefits {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  background: #000000;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.benefit-item span {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

/* File Uploader */
.file-uploader {
  width: 100%;
}

.upload-zone {
  border: 3px dashed #cbd5e1;
  border-radius: 16px;
  padding: 60px 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: #000000;
  background: #f1f5f9;
  transform: translateY(-2px);
}

.upload-icon {
  color: #64748b;
  margin-bottom: 20px;
}

.upload-text h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.upload-text p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 8px 0;
}

.browse-link {
  color: #000000;
  font-weight: 600;
  text-decoration: underline;
}

.upload-text small {
  font-size: 14px;
  color: #94a3b8;
}

/* Files Selected */
.files-selected {
  margin-top: 24px;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.files-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.clear-all-btn {
  background: transparent;
  color: #ef4444;
  border: 1px solid #ef4444;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: #ef4444;
  color: white;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.file-item:hover {
  background: #f1f5f9;
}

.file-icon {
  color: #64748b;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  word-break: break-word;
}

.file-size {
  font-size: 12px;
  color: #64748b;
}

.remove-file-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-file-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* Upload Info */
.upload-info {
  text-align: center;
}

.upload-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.file-types {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.file-type {
  background: #000000;
  color: white;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.upload-note {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

/* Processing Step */
.processing-step {
  text-align: center;
  padding: 60px 40px;
}

.processing-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.processing-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-animation h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.processing-animation p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.processing-steps {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.processing-step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.processing-step-item.active {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #64748b;
}

.processing-step-item.active .step-number {
  background: #000000;
  color: white;
}

/* Review Step */
.review-step {
  text-align: center;
  padding: 60px 40px;
}

.review-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.success-icon {
  color: #10b981;
}

.review-success h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.review-success p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  max-width: 400px;
}

.next-steps {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

/* Footer */
.bill-upload-footer {
  margin-top: 40px;
  text-align: center;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.security-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn-black {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.btn-black:hover {
  background: #1f2937;
  border-color: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: #000000;
  border-color: #000000;
}

.btn-outline:hover {
  background: #000000;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .bill-upload-section {
    padding: 60px 0;
  }

  .bill-upload-title {
    font-size: 36px;
  }

  .bill-upload-subtitle {
    font-size: 18px;
  }

  .bill-upload-content {
    padding: 24px;
  }

  .upload-zone {
    padding: 40px 20px;
  }

  .upload-benefits {
    grid-template-columns: 1fr;
  }

  .upload-actions,
  .next-steps {
    flex-direction: column;
  }

  .processing-steps {
    flex-direction: column;
    gap: 16px;
  }

  .security-badges {
    flex-direction: column;
    gap: 16px;
  }

  /* Progress Indicator Mobile */
  .progress-indicator {
    gap: 20px;
  }

  .progress-step:not(:last-child)::after {
    width: 20px;
  }

  /* Data Extraction Mobile */
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .data-form {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  /* Appointment Booking Mobile */
  .slots-grid {
    grid-template-columns: 1fr;
  }

  .appointment-form {
    padding: 20px;
  }

  .contact-methods {
    gap: 8px;
  }

  .contact-method {
    padding: 12px;
  }

  /* Completion Mobile */
  .completion-content h2 {
    font-size: 28px;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .completion-actions {
    flex-direction: column;
  }

  .social-proof {
    padding: 20px;
  }
}

/* Data Extraction Step */
.extraction-step {
  padding: 20px 0;
}

.extraction-header {
  text-align: center;
  margin-bottom: 32px;
}

.extraction-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.extraction-header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.extraction-processing {
  text-align: center;
  padding: 40px 20px;
}

.uploaded-files-summary {
  margin-top: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.uploaded-files-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.files-list-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-summary-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.file-summary-item .file-icon {
  font-size: 16px;
}

.file-summary-item .file-name {
  flex: 1;
  font-size: 14px;
  color: #1e293b;
}

.processing-status {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.extraction-error {
  text-align: center;
  padding: 40px 20px;
}

.extraction-error .error-icon {
  color: #ef4444;
  margin-bottom: 16px;
}

.extraction-error h3 {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.extraction-error p {
  font-size: 16px;
  color: #7f1d1d;
  margin: 0 0 24px 0;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.extraction-results {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.data-form {
  background: #f8fafc;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-group input.editing,
.form-group textarea.editing {
  border-color: #f59e0b;
  background: #fffbeb;
}

.field-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.authorization-section {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
  margin: 24px 0;
}

.authorization-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.authorization-checkbox input[type="checkbox"] {
  margin-top: 4px;
  width: 16px;
  height: 16px;
}

.authorization-checkbox label {
  font-size: 14px;
  line-height: 1.5;
  color: #92400e;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  margin-top: 32px;
}

.extraction-confidence {
  text-align: center;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.confidence-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.confidence-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.confidence-value {
  font-size: 18px;
  font-weight: 700;
  color: #15803d;
}

.confidence-note {
  font-size: 12px;
  color: #166534;
  margin: 0;
}

/* Appointment Booking Step */
.appointment-step {
  padding: 20px 0;
}

.appointment-header {
  text-align: center;
  margin-bottom: 32px;
}

.appointment-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.appointment-header p {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.appointment-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.client-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.client-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.client-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #374151;
}

.appointment-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.slot-card {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  background: white;
}

.slot-card:hover {
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slot-card.selected {
  border-color: #000000;
  background: #f8fafc;
}

.slot-date {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.slot-time {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8px;
}

.slot-status {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.contact-method:hover {
  border-color: #cbd5e1;
}

.contact-method input[type="radio"]:checked + .method-info {
  color: #000000;
}

.contact-method input[type="radio"]:checked {
  accent-color: #000000;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.method-icon {
  font-size: 24px;
}

.method-details h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.method-details p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.appointment-summary {
  margin-top: 24px;
  padding: 20px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.selected-appointment h4 {
  font-size: 16px;
  font-weight: 600;
  color: #166534;
  margin: 0 0 12px 0;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #15803d;
  font-weight: 600;
}

.appointment-error {
  text-align: center;
  padding: 40px 20px;
}

.appointment-error .error-icon {
  color: #ef4444;
  margin-bottom: 16px;
}

.appointment-error h3 {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
  margin: 0 0 8px 0;
}

.appointment-error p {
  font-size: 16px;
  color: #7f1d1d;
  margin: 0 0 24px 0;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* Completion Step */
.completion-step {
  padding: 20px 0;
  text-align: center;
}

.completion-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.success-animation {
  text-align: center;
}

.success-icon {
  color: #10b981;
  margin-bottom: 24px;
}

.completion-content h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.success-message {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}

.completion-summary {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.summary-section h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
}

.next-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  text-align: left;
}

.step-item .step-number {
  width: 32px;
  height: 32px;
  background: #000000;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.step-content p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.appointment-details-card {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 16px;
  padding: 24px;
}

.appointment-details-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #166534;
  margin: 0 0 16px 0;
}

.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #bbf7d0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #166534;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #15803d;
  font-weight: 600;
}

.important-notes {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 16px;
  padding: 24px;
}

.important-notes h3 {
  font-size: 20px;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 16px 0;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.note-icon {
  font-size: 16px;
  margin-top: 2px;
}

.note-item p {
  font-size: 14px;
  color: #92400e;
  margin: 0;
  line-height: 1.5;
}

.completion-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 20px;
}

.social-proof {
  background: #f8fafc;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
}

.testimonial {
  text-align: center;
  margin-bottom: 32px;
}

.testimonial p {
  font-size: 16px;
  font-style: italic;
  color: #374151;
  margin: 0 0 8px 0;
  line-height: 1.6;
}

.testimonial cite {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
