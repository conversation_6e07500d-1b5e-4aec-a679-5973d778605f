import React, { useState, useEffect } from 'react';
import billUploadService from '../../services/billUpload.service';

const DataExtractionStep = ({ uploadUUID, uploadedFiles, onDataConfirmed, onBack }) => {
  const [extractionStatus, setExtractionStatus] = useState('processing'); // 'processing', 'completed', 'error'
  const [extractedData, setExtractedData] = useState(null);
  const [editedData, setEditedData] = useState(null);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    if (uploadUUID) {
      performOCRExtraction();
    }
  }, [uploadUUID]);

  const performOCRExtraction = async () => {
    try {
      setExtractionStatus('processing');
      setError(null);
      
      console.log('🔍 Starting OCR extraction for UUID:', uploadUUID);
      
      // Simulate processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result = await billUploadService.extractBillData(uploadUUID);
      
      if (result.success) {
        setExtractedData(result.extractedData);
        setEditedData({ ...result.extractedData });
        setExtractionStatus('completed');
        console.log('✅ OCR extraction completed:', result.extractedData);
      } else {
        throw new Error('OCR extraction failed');
      }
      
    } catch (error) {
      console.error('❌ OCR extraction error:', error);
      setError(error.message);
      setExtractionStatus('error');
    }
  };

  const handleInputChange = (field, value) => {
    setEditedData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConfirmData = async () => {
    try {
      console.log('💾 Confirming and saving data:', editedData);
      
      const result = await billUploadService.saveClientData(uploadUUID, editedData);
      
      if (result.success) {
        console.log('✅ Data saved successfully');
        onDataConfirmed(editedData, result.clientId);
      } else {
        throw new Error('Failed to save data');
      }
      
    } catch (error) {
      console.error('❌ Error saving data:', error);
      setError(error.message);
    }
  };

  const retryExtraction = () => {
    performOCRExtraction();
  };

  if (extractionStatus === 'processing') {
    return (
      <div className="extraction-step">
        <div className="extraction-header">
          <h3>Analyzing Your Energy {uploadedFiles.length > 1 ? 'Bills' : 'Bill'}</h3>
          <p>Our AI is extracting key information from your uploaded {uploadedFiles.length > 1 ? 'documents' : 'document'}</p>
        </div>

        <div className="extraction-processing">
          <div className="processing-animation">
            <div className="processing-spinner"></div>
            <div className="processing-steps">
              <div className="processing-step-item active">
                <span className="step-number">1</span>
                <span>Reading documents</span>
              </div>
              <div className="processing-step-item active">
                <span className="step-number">2</span>
                <span>Extracting data</span>
              </div>
              <div className="processing-step-item">
                <span className="step-number">3</span>
                <span>Preparing results</span>
              </div>
            </div>
          </div>

          <div className="uploaded-files-summary">
            <h4>Processing {uploadedFiles.length} file{uploadedFiles.length > 1 ? 's' : ''}:</h4>
            <div className="files-list-summary">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="file-summary-item">
                  <span className="file-icon">📄</span>
                  <span className="file-name">{file.name}</span>
                  <span className="processing-status">✓ Processed</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (extractionStatus === 'error') {
    return (
      <div className="extraction-step">
        <div className="extraction-error">
          <div className="error-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
            </svg>
          </div>
          <h3>Extraction Failed</h3>
          <p>{error}</p>
          <div className="error-actions">
            <button className="btn btn-black" onClick={retryExtraction}>
              Try Again
            </button>
            <button className="btn btn-outline" onClick={onBack}>
              Upload Different Files
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="extraction-step">
      <div className="extraction-header">
        <h3>Verify Your Information</h3>
        <p>Please review and correct the extracted information from your energy bill{uploadedFiles.length > 1 ? 's' : ''}</p>
      </div>

      <div className="extraction-results">
        <div className="data-form">
          <div className="form-section">
            <h4>Client Information</h4>

            {/* Client type toggle */}
            <div className="client-type-toggle">
              <button
                type="button"
                className={`toggle-btn ${editedData?.clientType === 'individual' || (!editedData?.clientType && !editedData?.companyName) ? 'active' : ''}`}
                onClick={() => {
                  setEditedData(prev => ({
                    ...prev,
                    companyName: '',
                    fullName: prev.fullName || '',
                    clientType: 'individual'
                  }));
                }}
              >
                Individual
              </button>
              <button
                type="button"
                className={`toggle-btn ${editedData?.clientType === 'professional' || editedData?.companyName ? 'active' : ''}`}
                onClick={() => {
                  setEditedData(prev => ({
                    ...prev,
                    fullName: '',
                    companyName: prev.companyName || '',
                    clientType: 'professional'
                  }));
                }}
              >
                Professional
              </button>
            </div>

            {/* Show fullName field for individuals OR companyName field for professionals */}
            {editedData?.clientType === 'professional' || editedData?.companyName ? (
              // Professional client - show company name
              <div className="form-group">
                <label htmlFor="companyName">Company Name *</label>
                <input
                  type="text"
                  id="companyName"
                  value={editedData?.companyName || ''}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className={isEditing ? 'editing' : ''}
                  required
                />
                <small className="field-help">Professional client</small>
              </div>
            ) : (
              // Individual client - show full name
              <div className="form-group">
                <label htmlFor="fullName">Full Name *</label>
                <input
                  type="text"
                  id="fullName"
                  value={editedData?.fullName || ''}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  className={isEditing ? 'editing' : ''}
                  placeholder="Enter your full name"
                  required
                />
                <small className="field-help">Individual client</small>
              </div>
            )}

            <div className="form-group">
              <label htmlFor="email">Email Address *</label>
              <input
                type="email"
                id="email"
                value={editedData?.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={isEditing ? 'editing' : ''}
                required
              />
            </div>
          </div>

          <div className="form-section">
            <h4>Address & Energy Details</h4>
            <div className="form-group">
              <label htmlFor="address">Address *</label>
              <textarea
                id="address"
                value={editedData?.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className={isEditing ? 'editing' : ''}
                rows="3"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="pdlPrmRae">Point of Delivery (PDL/PRM/RAE) *</label>
              <input
                type="text"
                id="pdlPrmRae"
                value={editedData?.pdlPrmRae || ''}
                onChange={(e) => handleInputChange('pdlPrmRae', e.target.value)}
                className={isEditing ? 'editing' : ''}
                placeholder="14-digit identifier"
                required
              />
              <small className="field-help">
                This is your unique energy meter identifier (14 digits) found on your bill
              </small>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="provider">Energy Provider *</label>
                <input
                  type="text"
                  id="provider"
                  value={editedData?.provider || ''}
                  onChange={(e) => handleInputChange('provider', e.target.value)}
                  className={isEditing ? 'editing' : ''}
                  placeholder="e.g., ENGIE, EDF, Total Energies"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="energyType">Energy Type *</label>
                <select
                  id="energyType"
                  value={editedData?.energyType || 'electricity'}
                  onChange={(e) => handleInputChange('energyType', e.target.value)}
                  className={isEditing ? 'editing' : ''}
                  required
                >
                  <option value="electricity">Electricity</option>
                  <option value="gas">Gas</option>
                </select>
              </div>
            </div>
          </div>

          <div className="authorization-section">
            <div className="authorization-checkbox">
              <input
                type="checkbox"
                id="authorization"
                checked={isAuthorized}
                onChange={(e) => setIsAuthorized(e.target.checked)}
                required
              />
              <label htmlFor="authorization">
                <strong>I authorize My Energy Bill</strong> to collect and use my consumption data and to represent me in the renegotiation of my energy contracts with suppliers.
              </label>
            </div>
          </div>

          <div className="form-actions">
            <button className="btn btn-outline" onClick={onBack}>
              Back to Upload
            </button>
            <button
              className="btn btn-black btn-large"
              onClick={handleConfirmData}
              disabled={
                !editedData?.email ||
                !editedData?.address ||
                !editedData?.pdlPrmRae ||
                !editedData?.provider ||
                (editedData?.clientType === 'professional' && !editedData?.companyName) ||
                (editedData?.clientType === 'individual' && !editedData?.fullName) ||
                (!editedData?.clientType && !editedData?.fullName && !editedData?.companyName) ||
                !isAuthorized  // Authorization checkbox must be checked
              }
            >
              Confirm & Continue to Appointment
            </button>
          </div>
        </div>

        <div className="extraction-confidence">
          <div className="confidence-badge">
            <span className="confidence-label">Extraction Confidence:</span>
            <span className="confidence-value">95%</span>
          </div>
          <p className="confidence-note">
            High confidence extraction. Please verify all information is correct before proceeding.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DataExtractionStep;
