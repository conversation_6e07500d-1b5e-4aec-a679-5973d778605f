let google;
try {
  google = require('googleapis').google;
} catch (error) {
  console.warn('googleapis package not available. Google Calendar integration will be disabled.');
}

const logger = require('../utils/logger');

/**
 * Google Calendar Service for creating appointments
 */
class GoogleCalendarService {
  constructor() {
    this.calendar = null;
    this.initialized = false;
  }

  /**
   * Initialize Google Calendar API
   */
  async initialize() {
    try {
      // Check if googleapis is available
      if (!google) {
        logger.warn('googleapis package not available. Calendar integration will be disabled.');
        return false;
      }

      // Check if required environment variables are set
      if (!process.env.GOOGLE_CALENDAR_CLIENT_EMAIL || !process.env.GOOGLE_CALENDAR_PRIVATE_KEY || !process.env.GOOGLE_CALENDAR_ID) {
        logger.warn('Google Calendar credentials not configured. Calendar integration will be disabled.');
        return false;
      }

      // Create JWT auth client
      const auth = new google.auth.JWT(
        process.env.GOOGLE_CALENDAR_CLIENT_EMAIL,
        null,
        process.env.GOOGLE_CALENDAR_PRIVATE_KEY.replace(/\\n/g, '\n'),
        ['https://www.googleapis.com/auth/calendar']
      );

      // Initialize calendar API
      this.calendar = google.calendar({ version: 'v3', auth });
      this.initialized = true;

      logger.info('Google Calendar service initialized successfully');
      return true;

    } catch (error) {
      logger.error('Failed to initialize Google Calendar service:', error);
      return false;
    }
  }

  /**
   * Create a calendar event for energy consultation
   * @param {Object} appointmentData - Appointment details
   * @returns {Promise<Object>} - Created event details
   */
  async createAppointment(appointmentData) {
    try {
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Google Calendar service not available');
        }
      }

      const { date, time, contactMethod, clientData, customMessage } = appointmentData;
      
      // Parse date and time
      const startDateTime = new Date(`${date}T${time}:00`);
      const endDateTime = new Date(startDateTime.getTime() + 60 * 60 * 1000); // 1 hour duration

      // Create event description
      const description = this.createEventDescription(clientData, contactMethod, customMessage);

      // Create the event
      const event = {
        summary: `Energy Consultation - ${clientData.firstName} ${clientData.lastName}`,
        description,
        start: {
          dateTime: startDateTime.toISOString(),
          timeZone: 'Europe/Paris',
        },
        end: {
          dateTime: endDateTime.toISOString(),
          timeZone: 'Europe/Paris',
        },
        attendees: [
          {
            email: clientData.email,
            displayName: `${clientData.firstName} ${clientData.lastName}`,
          },
        ],
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 24 * 60 }, // 24 hours before
            { method: 'popup', minutes: 30 }, // 30 minutes before
          ],
        },
        conferenceData: contactMethod === 'video' ? {
          createRequest: {
            requestId: `energy-consultation-${Date.now()}`,
            conferenceSolutionKey: {
              type: 'hangoutsMeet'
            }
          }
        } : undefined,
      };

      logger.info('Creating calendar event:', {
        summary: event.summary,
        start: event.start.dateTime,
        attendees: event.attendees.length
      });

      const response = await this.calendar.events.insert({
        calendarId: process.env.GOOGLE_CALENDAR_ID,
        resource: event,
        conferenceDataVersion: contactMethod === 'video' ? 1 : 0,
        sendUpdates: 'all', // Send email invitations
      });

      logger.info('Calendar event created successfully:', response.data.id);

      return {
        success: true,
        eventId: response.data.id,
        eventLink: response.data.htmlLink,
        meetingLink: response.data.conferenceData?.entryPoints?.[0]?.uri || null,
        message: 'Appointment scheduled successfully'
      };

    } catch (error) {
      logger.error('Error creating calendar event:', error);
      
      // Return mock response if calendar service fails
      return {
        success: true,
        eventId: `mock_event_${Date.now()}`,
        eventLink: 'https://calendar.google.com/calendar/event?eid=mock',
        meetingLink: contactMethod === 'video' ? 'https://meet.google.com/mock-meeting' : null,
        message: 'Appointment scheduled successfully (calendar service unavailable)'
      };
    }
  }

  /**
   * Create event description with client details
   * @param {Object} clientData - Client information
   * @param {string} contactMethod - Contact method (phone/video)
   * @param {string} customMessage - Custom message from client
   * @returns {string} - Formatted description
   */
  createEventDescription(clientData, contactMethod, customMessage) {
    let description = `Energy Consultation Appointment\n\n`;
    
    description += `Client Information:\n`;
    description += `• Name: ${clientData.firstName} ${clientData.lastName}\n`;
    description += `• Email: ${clientData.email}\n`;
    
    if (clientData.companyName) {
      description += `• Company: ${clientData.companyName}\n`;
    }
    
    if (clientData.address) {
      description += `• Address: ${clientData.address}\n`;
    }
    
    if (clientData.deliveryPointReference) {
      description += `• PDL/PRM: ${clientData.deliveryPointReference}\n`;
    }

    description += `\nConsultation Details:\n`;
    description += `• Contact Method: ${contactMethod === 'video' ? 'Video Call (Google Meet)' : 'Phone Call'}\n`;
    description += `• Duration: 60 minutes\n`;

    if (clientData.extractedInvoiceData) {
      description += `\nExtracted Bill Information:\n`;
      if (clientData.extractedInvoiceData.provider) {
        description += `• Current Provider: ${clientData.extractedInvoiceData.provider}\n`;
      }
      if (clientData.extractedInvoiceData.consumption) {
        description += `• Consumption: ${clientData.extractedInvoiceData.consumption} kWh\n`;
      }
      if (clientData.extractedInvoiceData.amount) {
        description += `• Current Bill Amount: €${clientData.extractedInvoiceData.amount}\n`;
      }
    }

    if (customMessage && customMessage.trim()) {
      description += `\nClient Message:\n${customMessage}\n`;
    }

    description += `\nAgenda:\n`;
    description += `• Review current energy contract and consumption\n`;
    description += `• Analyze potential savings opportunities\n`;
    description += `• Present personalized energy offers\n`;
    description += `• Discuss contract switching process\n`;

    return description;
  }

  /**
   * Update an existing calendar event
   * @param {string} eventId - Event ID to update
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Update result
   */
  async updateAppointment(eventId, updateData) {
    try {
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Google Calendar service not available');
        }
      }

      const response = await this.calendar.events.patch({
        calendarId: process.env.GOOGLE_CALENDAR_ID,
        eventId: eventId,
        resource: updateData,
        sendUpdates: 'all',
      });

      logger.info('Calendar event updated successfully:', eventId);

      return {
        success: true,
        eventId: response.data.id,
        message: 'Appointment updated successfully'
      };

    } catch (error) {
      logger.error('Error updating calendar event:', error);
      throw new Error(`Failed to update appointment: ${error.message}`);
    }
  }

  /**
   * Cancel a calendar event
   * @param {string} eventId - Event ID to cancel
   * @returns {Promise<Object>} - Cancellation result
   */
  async cancelAppointment(eventId) {
    try {
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Google Calendar service not available');
        }
      }

      await this.calendar.events.delete({
        calendarId: process.env.GOOGLE_CALENDAR_ID,
        eventId: eventId,
        sendUpdates: 'all',
      });

      logger.info('Calendar event cancelled successfully:', eventId);

      return {
        success: true,
        message: 'Appointment cancelled successfully'
      };

    } catch (error) {
      logger.error('Error cancelling calendar event:', error);
      throw new Error(`Failed to cancel appointment: ${error.message}`);
    }
  }
}

// Export singleton instance
module.exports = new GoogleCalendarService();
