import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Auth } from 'aws-amplify';
import { clearAll } from '../utils/localStorage';
import { performLogout } from '../utils/logoutHandler';
import { showSuccessMessage, showErrorMessage } from '../utils/toastNotifications';
import { API_BASE_URL } from '../config/api-config';
import { STORAGE_KEYS, getItem } from '../utils/localStorage';
import logger from '../utils/logger';
import logoImage from '../assets/logo.jpeg';
import '../styles/sidebar.css';

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [userType, setUserType] = useState(null);
  const [isUserTypeLoaded, setIsUserTypeLoaded] = useState(false);
  const [userStatus, setUserStatus] = useState(null);
  const [isUserStatusLoaded, setIsUserStatusLoaded] = useState(false);

  const handleLogout = async () => {
    try {
      // Use the comprehensive logout handler
      await performLogout();

      // Show success message
      showSuccessMessage('LOGOUT_SUCCESS');

      // Redirect to login page with a state parameter indicating logout
      navigate('/login', { state: { fromLogout: true, timestamp: Date.now() } });
    } catch (error) {
      console.error('Error signing out:', error);

      // Show error message
      showErrorMessage('UNEXPECTED_ERROR', 'Logout completed, but there was an issue. You have been signed out.');

      // Even if there's an error, try one more time to clear everything
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (e) {
        // Silently fail
      }

      // Still redirect to login
      navigate('/login', { state: { fromLogout: true, timestamp: Date.now() } });
    }
  };
  const [isMobile, setIsMobile] = useState(false);

  // Function to fetch user status from API
  const fetchUserStatus = async () => {
    try {
      const cognitoId = getItem(STORAGE_KEYS.COGNITO_ID);
      const token = getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!cognitoId || !token) {
        logger.debug('Sidebar: Missing authentication data for status check');
        setIsUserStatusLoaded(true);
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/users/profile/${cognitoId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user status');
      }

      const data = await response.json();
      const user = data.data;

      setUserStatus(user.status);
      setIsUserStatusLoaded(true);
      logger.debug('Sidebar: User status fetched:', user.status);
    } catch (error) {
      logger.error('Sidebar: Error fetching user status:', error);
      setIsUserStatusLoaded(true);
    }
  };

  useEffect(() => {
    // Function to load user type with retry logic
    const loadUserType = () => {
      const storedUserType = localStorage.getItem('userType');
      console.log('Sidebar: userType from localStorage:', storedUserType);
      console.log('Sidebar: All localStorage keys:', Object.keys(localStorage));
      console.log('Sidebar: localStorage contents:', {
        userType: localStorage.getItem('userType'),
        userData: localStorage.getItem('userData'),
        isAuthenticated: localStorage.getItem('isAuthenticated')
      });

      if (storedUserType) {
        console.log('Sidebar: Setting userType to:', storedUserType);
        console.log('Sidebar: userType toLowerCase():', storedUserType.toLowerCase());
        console.log('Sidebar: Is broker?', storedUserType.toLowerCase() === 'broker');
        setUserType(storedUserType);
        setIsUserTypeLoaded(true);

        // If user is a broker or supplier, fetch their status
        if (['broker', 'supplier'].includes(storedUserType.toLowerCase())) {
          fetchUserStatus();
        } else {
          setIsUserStatusLoaded(true);
        }
      } else {
        console.log('Sidebar: No userType found, retrying...');
        // If no userType found, try again after a short delay
        setTimeout(() => {
          const retryUserType = localStorage.getItem('userType');
          console.log('Sidebar: retry userType from localStorage:', retryUserType);
          setUserType(retryUserType);
          setIsUserTypeLoaded(true);

          // If user is a broker or supplier, fetch their status
          if (retryUserType && ['broker', 'supplier'].includes(retryUserType.toLowerCase())) {
            fetchUserStatus();
          } else {
            setIsUserStatusLoaded(true);
          }
        }, 100);
      }
    };

    loadUserType();

    // Set initial mobile state
    setIsMobile(window.innerWidth < 992);

    // Add resize listener
    const handleResize = () => {
      setIsMobile(window.innerWidth < 992);
    };

    window.addEventListener('resize', handleResize);

    // Listen for localStorage changes
    const handleStorageChange = (e) => {
      if (e.key === 'userType') {
        console.log('Sidebar: userType changed in localStorage:', e.newValue);
        setUserType(e.newValue);
        setIsUserTypeLoaded(true);
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Also listen for location changes to re-check userType
  useEffect(() => {
    const storedUserType = localStorage.getItem('userType');
    console.log('Sidebar: location changed, userType:', storedUserType);
    if (storedUserType !== userType) {
      setUserType(storedUserType);
      setIsUserTypeLoaded(true);
    }
  }, [location.pathname]);

  // Check if the current path matches the link path
  const isActive = (path) => {
    return location.pathname === path;
  };

  // Check if navigation should be disabled for brokers/suppliers
  const isNavigationDisabled = () => {
    if (!isUserTypeLoaded || !isUserStatusLoaded) return false;

    const normalizedUserType = userType?.toLowerCase();
    if (!['broker', 'supplier'].includes(normalizedUserType)) return false;

    // Disable navigation for limited access statuses
    const limitedAccessStatuses = ['Profile_Submitted', 'Under_Review', 'Changes_Requested', 'Pending'];
    return limitedAccessStatuses.includes(userStatus);
  };

  // Custom Link component that closes the sidebar on mobile and handles disabled state
  const SidebarLink = ({ to, children, disabled = false }) => {
    const handleClick = (e) => {
      if (disabled) {
        e.preventDefault();
        return;
      }

      // Close sidebar on mobile after navigation
      if (window.innerWidth < 992) {
        toggleSidebar();
      }
    };

    if (disabled) {
      return (
        <span className="sidebar-link-disabled" title="Available after account approval">
          {children}
        </span>
      );
    }

    return (
      <Link to={to} className="sidebar-nav-link" onClick={handleClick}>
        {children}
      </Link>
    );
  };

  // Debug: Log userType whenever it changes
  console.log('Sidebar render - userType:', userType, 'isUserTypeLoaded:', isUserTypeLoaded, 'location:', location.pathname);
  console.log('Sidebar render - userType === "admin":', userType === 'admin');
  console.log('Sidebar render - userType !== "admin":', userType !== 'admin');

  return (
    <>
      {/* Overlay for mobile when sidebar is open - only show on mobile */}
      {isMobile && (
        <div
          className={`sidebar-overlay ${isOpen ? 'active' : ''}`}
          onClick={toggleSidebar}
          style={{ display: isOpen ? 'block' : 'none' }}
        ></div>
      )}

      {/* Sidebar */}
      <div className={`sidebar ${isOpen ? 'open' : ''}`}>
        <div className="sidebar-header">
          {isOpen && (
            <div className="sidebar-logo">
              <img src={logoImage} alt="MY ENERGY BILL" width="50" height="50" />
              <span>MY ENERGY BILL</span>
            </div>
          )}
          {isOpen ? (
            <button
              className="sidebar-toggle-btn"
              onClick={toggleSidebar}
              aria-label="Collapse Sidebar"
            >
              <i className="fas fa-chevron-left"></i>
            </button>
          ) : null}
          {/* Separate button for collapsed state to ensure proper positioning */}
          {!isOpen && (
            <div className="collapsed-toggle-container">
              <button
                className="sidebar-toggle-btn collapsed"
                onClick={toggleSidebar}
                aria-label="Expand Sidebar"
              >
                <i className="fas fa-bars"></i>
              </button>
            </div>
          )}
        </div>

        <div className="sidebar-content">
          <ul className="sidebar-menu">
            {/* Dashboard - same route for all users, but different content based on userType */}
            <li className={isActive('/dashboard') ? 'active' : ''}>
              <SidebarLink to="/dashboard">
                <i className="fas fa-home"></i>
                <span>Dashboard</span>
              </SidebarLink>
            </li>

            {/* Show loading state if userType is not yet loaded */}
            {!isUserTypeLoaded && (
              <li>
                <div style={{ padding: '10px', textAlign: 'center', color: '#666' }}>
                  <i className="fas fa-spinner fa-spin"></i>
                  <span style={{ marginLeft: '8px' }}>Loading...</span>
                </div>
              </li>
            )}

            {/* Admin specific navigation */}
            {isUserTypeLoaded && userType?.toLowerCase() === 'admin' && (
              <>
                <li className={isActive('/admin/users') ? 'active' : ''}>
                  <SidebarLink to="/admin/users">
                    <i className="fas fa-users"></i>
                    <span>Manage Users</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/brokers') ? 'active' : ''}>
                  <SidebarLink to="/admin/brokers">
                    <i className="fas fa-handshake"></i>
                    <span>Manage Brokers</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/suppliers') ? 'active' : ''}>
                  <SidebarLink to="/admin/suppliers">
                    <i className="fas fa-industry"></i>
                    <span>Manage Suppliers</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/contracts') ? 'active' : ''}>
                  <SidebarLink to="/admin/contracts">
                    <i className="fas fa-file-contract"></i>
                    <span>All Contracts</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/quotes') ? 'active' : ''}>
                  <SidebarLink to="/admin/quotes">
                    <i className="fas fa-calculator"></i>
                    <span>Quote Management</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/invitations') ? 'active' : ''}>
                  <SidebarLink to="/admin/invitations">
                    <i className="fas fa-envelope"></i>
                    <span>Invitations</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/templates') ? 'active' : ''}>
                  <SidebarLink to="/admin/templates">
                    <i className="fas fa-file-alt"></i>
                    <span>Document Templates</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/notifications') ? 'active' : ''}>
                  <SidebarLink to="/admin/notifications">
                    <i className="fas fa-bell"></i>
                    <span>Notifications</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/support-tickets') ? 'active' : ''}>
                  <SidebarLink to="/admin/support-tickets">
                    <i className="fas fa-ticket-alt"></i>
                    <span>Support Tickets</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/reports') ? 'active' : ''}>
                  <SidebarLink to="/admin/reports">
                    <i className="fas fa-chart-bar"></i>
                    <span>Reports & Analytics</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/admin/settings') ? 'active' : ''}>
                  <SidebarLink to="/admin/settings">
                    <i className="fas fa-cogs"></i>
                    <span>System Settings</span>
                  </SidebarLink>
                </li>
              </>
            )}

            {/* Show invoices and offers only for individual and professional users */}
            {isUserTypeLoaded && (userType?.toLowerCase() === 'individual' || userType?.toLowerCase() === 'professional') && (
              <>
                <li className={isActive('/invoices') ? 'active' : ''}>
                  <SidebarLink to="/invoices">
                    <i className="fas fa-file-invoice"></i>
                    <span>My Invoices</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/offers') ? 'active' : ''}>
                  <SidebarLink to="/offers">
                    <i className="fas fa-tags"></i>
                    <span>Personalized Offers</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/contracts') ? 'active' : ''}>
                  <SidebarLink to="/contracts">
                    <i className="fas fa-file-signature"></i>
                    <span>My Contracts</span>
                  </SidebarLink>
                </li>
              </>
            )}

            {/* Appointments are relevant for all user types except admin */}
            {isUserTypeLoaded && userType?.toLowerCase() !== 'admin' && (
              <li className={isActive('/appointments') ? 'active' : ''}>
                <SidebarLink to="/appointments">
                  <i className="fas fa-calendar-alt"></i>
                  <span>My Appointments</span>
                </SidebarLink>
              </li>
            )}

            {/* Divider - only show for non-admin users */}
            {isUserTypeLoaded && userType?.toLowerCase() !== 'admin' && <li className="sidebar-divider"></li>}

            {/* User type specific menu items */}
            {isUserTypeLoaded && userType?.toLowerCase() === 'professional' && (
              <li className={isActive('/business-dashboard') ? 'active' : ''}>
                <SidebarLink to="/business-dashboard">
                  <i className="fas fa-briefcase"></i>
                  <span>Business Dashboard</span>
                </SidebarLink>
              </li>
            )}

            {isUserTypeLoaded && userType?.toLowerCase() === 'supplier' && (
              <>
                <li className={`${isActive('/my-offers') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/my-offers" disabled={isNavigationDisabled()}>
                    <i className="fas fa-bolt"></i>
                    <span>My Energy Offers</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/supplier-contracts') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/supplier-contracts" disabled={isNavigationDisabled()}>
                    <i className="fas fa-file-contract"></i>
                    <span>Customer Contracts</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/create-offer') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/create-offer" disabled={isNavigationDisabled()}>
                    <i className="fas fa-plus-circle"></i>
                    <span>Create New Offer</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/analytics') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/analytics" disabled={isNavigationDisabled()}>
                    <i className="fas fa-chart-line"></i>
                    <span>Performance Analytics</span>
                  </SidebarLink>
                </li>
              </>
            )}

            {(() => {
              console.log('🔍 SIDEBAR BROKER CHECK:', {
                isUserTypeLoaded,
                userType,
                userTypeLower: userType?.toLowerCase(),
                isBroker: userType?.toLowerCase() === 'broker',
                shouldShowBrokerMenu: isUserTypeLoaded && userType?.toLowerCase() === 'broker',
                userStatus,
                isUserStatusLoaded,
                isNavigationDisabled: isNavigationDisabled()
              });
              return isUserTypeLoaded && userType?.toLowerCase() === 'broker';
            })() && (
              <>
                <li className={`${isActive('/clients') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/clients" disabled={isNavigationDisabled()}>
                    <i className="fas fa-users"></i>
                    <span>My Clients</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/broker-deals') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/broker-deals" disabled={isNavigationDisabled()}>
                    <i className="fas fa-handshake"></i>
                    <span>Active Deals</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/add-client') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/add-client" disabled={isNavigationDisabled()}>
                    <i className="fas fa-user-plus"></i>
                    <span>Add New Client</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/commission-tracker') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/commission-tracker" disabled={isNavigationDisabled()}>
                    <i className="fas fa-euro-sign"></i>
                    <span>Commission Tracker</span>
                  </SidebarLink>
                </li>

                <li className={`${isActive('/analytics') ? 'active' : ''} ${isNavigationDisabled() ? 'disabled' : ''}`}>
                  <SidebarLink to="/analytics" disabled={isNavigationDisabled()}>
                    <i className="fas fa-chart-line"></i>
                    <span>Sales Analytics</span>
                  </SidebarLink>
                </li>
              </>
            )}

            {/* Common menu items - only for non-admin users */}
            {isUserTypeLoaded && userType?.toLowerCase() !== 'admin' && (
              <>
                <li className={isActive('/profile') ? 'active' : ''}>
                  <SidebarLink to="/profile">
                    <i className="fas fa-user"></i>
                    <span>My Profile</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/settings') ? 'active' : ''}>
                  <SidebarLink to="/settings">
                    <i className="fas fa-cog"></i>
                    <span>Settings</span>
                  </SidebarLink>
                </li>

                <li className={isActive('/support') ? 'active' : ''}>
                  <SidebarLink to="/support">
                    <i className="fas fa-question-circle"></i>
                    <span>Help & Support</span>
                  </SidebarLink>
                </li>
              </>
            )}


          </ul>
        </div>

        <div className="sidebar-footer">
          <button className="logout-button" onClick={handleLogout}>
            <i className="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
