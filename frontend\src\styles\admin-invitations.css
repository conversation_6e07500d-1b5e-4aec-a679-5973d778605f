/* Invitation Management - Black & White Theme */
.invitation-management-container {
  padding: 32px;
  width: 100%;
  max-width: 100%;
  margin: 0;
  min-height: calc(100vh - 80px);
  background: #ffffff;
  position: relative;
  overflow-x: hidden;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.header-content h1 {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;
}

.header-content p {
  margin: 0;
  color: #333333;
  font-size: 16px;
  font-weight: 500;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #333333;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #f5f5f5;
  color: #000000;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.invitation-management-container .btn-primary,
.invitation-management-container .btn-secondary {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: 1px solid #000000;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.invitation-management-container .btn-primary {
  background: #000000;
  color: #ffffff;
}

.invitation-management-container .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #333333;
}

.invitation-management-container .btn-secondary {
  background: #ffffff;
  color: #000000;
}

.invitation-management-container .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
}

/* Statistics Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.stat-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 28px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #000000;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #000000;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000;
  color: #ffffff;
  font-size: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.stat-content h3 {
  margin: 0 0 6px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #000000;
}

.stat-content p {
  margin: 0;
  color: #333333;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filters */
.filters-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 28px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  position: relative;
  z-index: 1;
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 24px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 600;
  color: #000000;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group select,
.filter-group input {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: #ffffff;
  color: #000000;
  transition: all 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Table */
.table-container {
  background: #ffffff;
  border-radius: 8px;
  overflow-x: auto;
  overflow-y: visible;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
}

.invitations-table {
  width: 100%;
  min-width: 800px; /* Ensure minimum width for proper column display */
  border-collapse: collapse;
  table-layout: fixed; /* Enable fixed table layout for better control */
}

.invitations-table th {
  background: #f5f5f5;
  padding: 20px 16px;
  text-align: left;
  font-weight: 700;
  color: #000000;
  border-bottom: 2px solid #e0e0e0;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  white-space: nowrap;
}

.invitations-table td {
  padding: 20px 16px;
  border-bottom: 1px solid #f5f5f5;
  vertical-align: top;
  transition: all 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
}

.invitations-table td:last-child {
  vertical-align: middle;
}

.invitations-table tr:hover {
  background: #f5f5f5;
}

.invitations-table tr:nth-child(even) {
  background: #fafafa;
}

.invitations-table tr:nth-child(even):hover {
  background: #f0f0f0;
}

/* Invitee Info */
.invitee-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.invitee-name {
  font-weight: 500;
  color: #2c3e50;
}

.invitee-email {
  color: #6c757d;
  font-size: 14px;
}

.invitee-company {
  color: #007bff;
  font-size: 12px;
  font-style: italic;
}

/* Badges */
.type-badge {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  border: 1px solid #e0e0e0;
}

.type-badge.broker,
.type-badge.supplier {
  background: #f5f5f5;
  color: #000000;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  border: 1px solid #e0e0e0;
}

.status-badge.pending {
  background: #666666;
  color: #ffffff;
  border-color: #666666;
}

.status-badge.accepted {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.status-badge.expired,
.status-badge.revoked {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

/* Expiry Info */
.expiry-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.days-remaining {
  font-size: 12px;
  color: #28a745;
  font-weight: 500;
}

.days-remaining.urgent {
  color: #dc3545;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1px;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: auto;
  flex-wrap: nowrap;
}

button.action-btn,
.action-btn {
  width: 16px !important;
  height: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  max-width: 16px !important;
  max-height: 16px !important;
  border: none !important;
  border-radius: 2px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 8px !important;
  font-weight: 600 !important;
  background: #f8f9fa !important;
  color: #333333 !important;
  box-shadow: none !important;
  flex-shrink: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
}

.action-btn:hover {
  transform: scale(1.05);
}

.action-btn.approve {
  background: #28a745 !important;
  color: white !important;
  border-color: #28a745 !important;
}

.action-btn.approve:hover {
  background: #218838 !important;
  transform: scale(1.05);
}

.action-btn.reject {
  background: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
}

.action-btn.reject:hover {
  background: #c82333 !important;
  transform: scale(1.05);
}

.action-btn.resend {
  background: #007bff !important;
  color: white !important;
  border-color: #007bff !important;
}

.action-btn.resend:hover {
  background: #0056b3 !important;
  border-color: #0056b3 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.action-btn.revoke {
  background: #6c757d !important;
  color: white !important;
  border-color: #6c757d !important;
}

.action-btn.revoke:hover {
  background: #545b62 !important;
  border-color: #545b62 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
}

.action-btn.resend,
.action-btn.view {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.action-btn.resend:hover,
.action-btn.view:hover {
  background: #333333;
}

.action-btn.revoke {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.action-btn.revoke:hover {
  background: #000000;
  color: #ffffff;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #dee2e6;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal styles removed - using modal-system.css instead */

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 28px;
  padding-top: 24px;
  border-top: 2px solid #e0e0e0;
}

.btn-cancel {
  padding: 12px 24px;
  border: 1px solid #e0e0e0;
  background: #ffffff;
  color: #333333;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: #f5f5f5;
  color: #000000;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #000000;
}

.invitation-management-container .btn-confirm {
  padding: 12px 24px;
  border: 1px solid #000000;
  background: #000000;
  color: #ffffff;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #333333;
}

.btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Form Styles */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.form-group label {
  font-weight: 600;
  color: #000000;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 14px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: #ffffff;
  color: #000000;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group input[type="file"] {
  padding: 12px;
  background: #f5f5f5;
  border-style: dashed;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.checkbox-label:hover {
  background: #e0e0e0;
  border-color: #000000;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: #000000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
  }

  .header-actions button {
    flex: 1;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filters-row {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
  }

  .invitations-table {
    min-width: 650px; /* Increased minimum width for mobile */
    font-size: 14px;
  }

  .invitations-table th,
  .invitations-table td {
    padding: 12px 8px;
  }


}
