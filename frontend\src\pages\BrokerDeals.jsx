import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../components/DashboardLayout';
import Spinner from '../components/Spinner';
import { showErrorMessage } from '../utils/toastNotifications';
import brokerService from '../services/broker.service';
import logger from '../utils/logger';
import '../styles/clients.css';

const BrokerDeals = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [deals, setDeals] = useState([]);
  const [filteredDeals, setFilteredDeals] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    completed: 0,
    pending: 0,
    totalCommission: 0
  });

  useEffect(() => {
    fetchDeals();
  }, []);

  useEffect(() => {
    filterDeals();
  }, [deals, searchTerm, statusFilter]);

  const fetchDeals = async () => {
    try {
      setLoading(true);
      logger.info('Fetching broker deals...');
      
      // For now, using mock data since the backend endpoint might not be fully implemented
      const mockDeals = [
        {
          id: '1',
          clientName: 'ABC Corporation',
          clientEmail: '<EMAIL>',
          supplier: 'EDF',
          energyType: 'Electricity',
          contractValue: 15000,
          commission: 750,
          commissionRate: 5,
          status: 'Active',
          startDate: '2024-01-15',
          endDate: '2025-01-15',
          createdDate: '2024-01-10',
          notes: 'Large commercial client with high consumption'
        },
        {
          id: '2',
          clientName: 'Green Solutions Ltd',
          clientEmail: '<EMAIL>',
          supplier: 'Engie',
          energyType: 'Gas',
          contractValue: 8500,
          commission: 425,
          commissionRate: 5,
          status: 'Completed',
          startDate: '2023-12-01',
          endDate: '2024-12-01',
          createdDate: '2023-11-25',
          notes: 'Renewable energy focused client'
        },
        {
          id: '3',
          clientName: 'Tech Startup Inc',
          clientEmail: '<EMAIL>',
          supplier: 'Total Energies',
          energyType: 'Both',
          contractValue: 12000,
          commission: 600,
          commissionRate: 5,
          status: 'Pending',
          startDate: '2024-02-01',
          endDate: '2025-02-01',
          createdDate: '2024-01-20',
          notes: 'Fast-growing startup with expanding energy needs'
        }
      ];

      setDeals(mockDeals);
      
      // Calculate stats
      const statsData = {
        total: mockDeals.length,
        active: mockDeals.filter(deal => deal.status === 'Active').length,
        completed: mockDeals.filter(deal => deal.status === 'Completed').length,
        pending: mockDeals.filter(deal => deal.status === 'Pending').length,
        totalCommission: mockDeals.reduce((sum, deal) => sum + deal.commission, 0)
      };
      setStats(statsData);
      
      logger.info('Broker deals fetched successfully');
    } catch (error) {
      logger.error('Error fetching broker deals:', error);
      showErrorMessage('FETCH_DEALS_ERROR', 'Failed to load deals');
    } finally {
      setLoading(false);
    }
  };

  const filterDeals = () => {
    let filtered = deals;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(deal =>
        deal.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.energyType.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(deal => deal.status.toLowerCase() === statusFilter.toLowerCase());
    }

    setFilteredDeals(filtered);
  };

  const getStatusBadgeClass = (status) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'status-badge active';
      case 'completed':
        return 'status-badge completed';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="clients-container">
          <div className="loading-container">
            <Spinner size="large" message="Loading deals..." />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="clients-container">
        <div className="clients-header">
          <div className="header-content">
            <h1>
              <i className="fas fa-handshake"></i>
              Active Deals
            </h1>
            <p>Manage and track your energy deals and commissions</p>
          </div>
          <div className="header-actions">
            <button className="btn-primary" onClick={() => navigate('/add-client')}>
              <i className="fas fa-plus"></i>
              New Deal
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-handshake"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.total}</h3>
              <p>Total Deals</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.active}</h3>
              <p>Active Deals</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-clock"></i>
            </div>
            <div className="stat-content">
              <h3>{stats.pending}</h3>
              <p>Pending Deals</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-euro-sign"></i>
            </div>
            <div className="stat-content">
              <h3>{formatCurrency(stats.totalCommission)}</h3>
              <p>Total Commission</p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="clients-filters">
          <div className="search-box">
            <i className="fas fa-search"></i>
            <input
              type="text"
              placeholder="Search deals by client, supplier, or energy type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>

        {/* Deals List */}
        <div className="clients-list">
          {filteredDeals.length > 0 ? (
            filteredDeals.map((deal) => (
              <div key={deal.id} className="client-card">
                <div className="client-header">
                  <div className="client-info">
                    <h3>{deal.clientName}</h3>
                    <p>{deal.clientEmail}</p>
                  </div>
                  <div className="client-status">
                    <span className={getStatusBadgeClass(deal.status)}>
                      {deal.status}
                    </span>
                  </div>
                </div>
                <div className="client-details">
                  <div className="detail-item">
                    <span className="label">Supplier:</span>
                    <span className="value">{deal.supplier}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Energy Type:</span>
                    <span className="value">{deal.energyType}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Contract Value:</span>
                    <span className="value">{formatCurrency(deal.contractValue)}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Commission:</span>
                    <span className="value commission">{formatCurrency(deal.commission)} ({deal.commissionRate}%)</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Contract Period:</span>
                    <span className="value">{formatDate(deal.startDate)} - {formatDate(deal.endDate)}</span>
                  </div>
                  {deal.notes && (
                    <div className="detail-item full-width">
                      <span className="label">Notes:</span>
                      <span className="value">{deal.notes}</span>
                    </div>
                  )}
                </div>
                <div className="client-actions">
                  <button className="btn-secondary">
                    <i className="fas fa-eye"></i>
                    View Details
                  </button>
                  <button className="btn-secondary">
                    <i className="fas fa-edit"></i>
                    Edit
                  </button>
                  <button className="btn-secondary">
                    <i className="fas fa-file-contract"></i>
                    Contract
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="empty-state">
              <i className="fas fa-handshake"></i>
              <h3>No deals found</h3>
              <p>
                {searchTerm || statusFilter !== 'all'
                  ? 'No deals match your current filters.'
                  : 'You haven\'t created any deals yet.'}
              </p>
              <button className="btn-primary" onClick={() => navigate('/add-client')}>
                <i className="fas fa-plus"></i>
                Create Your First Deal
              </button>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BrokerDeals;
