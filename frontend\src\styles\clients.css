.clients-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.clients-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.clients-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #000;
  margin: 0;
}

.add-client-btn {
  background: linear-gradient(to right, #000, #333);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.add-client-btn i {
  margin-right: 8px;
}

.add-client-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.clients-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #000;
  outline: none;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

.filter-btn:hover:not(.active) {
  background-color: #f5f5f5;
}

.clients-table-container {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #000;
}

.clients-table {
  width: 100%;
  border-collapse: collapse;
}

.clients-table th,
.clients-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eaeaea;
}

.clients-table th {
  background-color: #f9f9f9;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.clients-table tr:last-child td {
  border-bottom: none;
}

.clients-table tbody tr {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clients-table tbody tr:hover {
  background-color: #f9f9f9;
}

.client-name {
  font-weight: 500;
  color: #000;
}

.client-type {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.client-type.individual {
  background-color: #e3f2fd;
  color: #1565c0;
}

.client-type.business {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.client-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #757575;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.client-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fff;
}

.view-btn {
  background-color: #000;
}

.edit-btn {
  background-color: #333;
}

.message-btn {
  background-color: #555;
}

.action-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.no-clients {
  text-align: center;
  padding: 30px;
  color: #666;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 992px) {
  .hide-tablet {
    display: none;
  }
}

@media (max-width: 768px) {
  .clients-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .clients-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .hide-mobile {
    display: none;
  }
}

/* Add Client Form Styles */
.add-client-form-container {
  max-width: 800px;
  margin: 0 auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.add-client-form {
  padding: 2rem;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f3f4f6;
}

/* Comparison Criteria Styles */
.comparison-criteria {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.comparison-criteria h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.criteria-form .form-row {
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 0;
}

/* Offers Grid Styles */
.offers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.offer-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #e9ecef;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.offer-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.offer-card.selected {
  border-color: #000;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.offer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.supplier-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.supplier-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.offer-rating {
  text-align: right;
}

.stars {
  color: #ffc107;
  margin-bottom: 0.25rem;
}

.rating-value {
  font-size: 0.85rem;
  color: #6c757d;
}

.offer-pricing {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-item.highlight {
  font-weight: 600;
  color: #2c3e50;
  border-top: 1px solid #dee2e6;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.offer-features {
  margin-bottom: 1rem;
}

.offer-features h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

.offer-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.offer-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: #374151;
}

.offer-features li i {
  color: #27ae60;
  font-size: 0.75rem;
}

.offer-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.detail-item .label {
  color: #6c757d;
}

.detail-item .value.commission {
  color: #27ae60;
  font-weight: 600;
}

.green-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: #d4edda;
  color: #155724;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0.5rem 0;
}

.savings {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  color: #27ae60;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
}

.offer-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.offer-actions .btn-secondary.selected {
  background: #000;
  color: #fff;
  border-color: #000;
}

/* Comparison Table Styles */
.comparison-table-container {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  margin-top: 2rem;
  overflow: hidden;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.comparison-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.comparison-table {
  overflow-x: auto;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.comparison-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.comparison-table td.highlight {
  font-weight: 600;
  color: #2c3e50;
}

.comparison-table td.commission {
  color: #27ae60;
  font-weight: 600;
}

/* Additional responsive styles for new components */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .criteria-form .form-row {
    grid-template-columns: 1fr;
  }

  .offers-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .comparison-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
