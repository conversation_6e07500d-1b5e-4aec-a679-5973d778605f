const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: [
      'OfferReceived',
      'ContractSigned',
      'AppointmentScheduled',
      'AppointmentReminder',
      'RequestStatusUpdate',
      'DocumentVerified',
      'PaymentReceived',
      'SystemAlert',
      'BrokerRegistration',
      'BrokerApproval',
      'BrokerRejection',
      'Other'
    ],
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  read: {
    type: Boolean,
    default: false
  },
  readAt: {
    type: Date
  },
  expiresAt: {
    type: Date
  },
  actionUrl: {
    type: String
  },
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['EnergyRequest', 'Offer', 'Contract', 'Appointment', 'SupportTicket', 'Document']
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId
    }
  },
  priority: {
    type: String,
    enum: ['Low', 'Normal', 'High'],
    default: 'Normal'
  },
  deliveryChannels: {
    inApp: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: false
    },
    sms: {
      type: Boolean,
      default: false
    },
    push: {
      type: Boolean,
      default: false
    }
  },
  deliveryStatus: {
    inApp: {
      type: String,
      enum: ['Pending', 'Sent', 'Failed'],
      default: 'Pending'
    },
    email: {
      type: String,
      enum: ['Pending', 'Sent', 'Failed', 'NotApplicable'],
      default: 'NotApplicable'
    },
    sms: {
      type: String,
      enum: ['Pending', 'Sent', 'Failed', 'NotApplicable'],
      default: 'NotApplicable'
    },
    push: {
      type: String,
      enum: ['Pending', 'Sent', 'Failed', 'NotApplicable'],
      default: 'NotApplicable'
    }
  }
}, {
  timestamps: true
});

// Create indexes
notificationSchema.index({ userId: 1 });
notificationSchema.index({ read: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ createdAt: -1 });

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
