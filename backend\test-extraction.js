// Test script for client data extraction
const { extractClientDataFromTextLines } = require('./routes/energyBills');

// Sample text lines from ENGIE bill
const sampleTextLines = [
  "ENGIE",
  "",
  "Entreprises & Collectivités",
  "",
  "Vos références",
  "Référence client 300 001 631 510",
  "Titulaire du contrat CREPERIE DU MANOIR BRETON",
  "SIRET du contractant 33892067000010",
  "",
  "Vos contacts",
  "Pour nous contacter ou accéder",
  "à vos contrats et factures:",
  "votre Espace Client BILL-e",
  "en téléphonant 24/7",
  "au 0969 324 324",
  "",
  "Votre responsable clientèle NICOLAS MUNOZ",
  "",
  "Du lundi au vendredi de 8h30 à 18h",
  "tél. 0228032756",
  "",
  "<EMAIL>",
  "",
  "Locaux",
  "TSA 26703",
  "37927 TOURS CEDEX 9",
  "",
  "Dépannage réseau 24/24",
  "0811 882 200",
  "",
  "CREPERIE DU MANOIR BRETON",
  "18 RUE D ODESSA",
  "BF 010", 
  "75014 PARIS",
  "",
  "Votre Facture Monosite électricité",
  "N° 420006784903 - 10 avril 2025",
  "À RÉGLER AVANT LE 25/04/2025",
  "Votre mode de paiement PRÉLÈVEMENT AUTOMATIQUE",
  "Votre prochain prélèvement vers le 15 mai 2025",
  "",
  "MONTANT TTC à payer 3 384,54 €",
  "",
  "Consommation totale d'électricité 7 468 kWh",
  "",
  "Électricité",
  "• Fourniture d'électricité",
  "Terme fixe fourniture 16,99 €",
  "Terme variable fourniture 1 954,53 €",
  "Obligation Capacité 50,36 €",
  "Contribution CEE 53,92 €"
];

console.log('🧪 Testing client data extraction with ENGIE bill sample...');
console.log('📝 Sample text lines:', sampleTextLines.length);

// Test the extraction function
try {
  const result = extractClientDataFromTextLines(sampleTextLines);
  console.log('✅ Extraction completed!');
  console.log('📋 Results:');
  console.log(JSON.stringify(result, null, 2));
  
  // Check what we expected vs what we got
  console.log('\n🔍 Analysis:');
  console.log('Expected company name: "CREPERIE DU MANOIR BRETON"');
  console.log('Extracted company name:', result.companyName);
  console.log('Expected fullName: "" (empty for professional)');
  console.log('Extracted fullName:', result.fullName);
  console.log('Expected address: "18 RUE D ODESSA, BF 010, 75014 PARIS"');
  console.log('Extracted address:', result.address);
  console.log('Expected email: "<EMAIL>"');
  console.log('Extracted email:', result.email);
  console.log('Expected provider: "ENGIE"');
  console.log('Extracted provider:', result.provider);
  console.log('Expected energyType: "electricity"');
  console.log('Extracted energyType:', result.energyType);
  console.log('Expected PDL/PRM/RAE: "" (none in test data)');
  console.log('Extracted PDL/PRM/RAE:', result.pdlPrmRae);
  console.log('Expected clientType: "professional"');
  console.log('Extracted clientType:', result.clientType);

  // Validation
  console.log('\n✅ Validation:');
  console.log('Company name correct:', result.companyName === 'CREPERIE DU MANOIR BRETON' ? '✅' : '❌');
  console.log('Address correct:', result.address === '18 RUE D ODESSA, BF 010, 75014 PARIS' ? '✅' : '❌');
  console.log('Email correct:', result.email === '<EMAIL>' ? '✅' : '❌');
  console.log('Provider correct:', result.provider === 'ENGIE' ? '✅' : '❌');
  console.log('Energy type correct:', result.energyType === 'electricity' ? '✅' : '❌');
  console.log('PDL/PRM/RAE empty (no test data):', result.pdlPrmRae === '' ? '✅' : '❌');
  console.log('Full name empty (professional):', result.fullName === '' ? '✅' : '❌');
  console.log('Client type correct:', result.clientType === 'professional' ? '✅' : '❌');
  
} catch (error) {
  console.error('❌ Error during extraction:', error);
  console.error(error.stack);
}
